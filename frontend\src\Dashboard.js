import React, { useState } from 'react';
import { useChannels } from '../hooks/useChannels';
import Sidebar from '../components/Sidebar';
import ChannelCard from '../components/ChannelCard';
import AddChannelForm from '../components/AddChannelForm';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './Dashboard.css';

const Dashboard = () => {
  const { channels, loading, error, addChannel, removeChannel, refreshChannel } = useChannels();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(null);

  const handleAddChannel = async (channelData) => {
    try {
      await addChannel(channelData);
      setShowAddForm(false);
      toast.success('Channel added successfully!');
    } catch (error) {
      toast.error(error.message || 'Failed to add channel');
    }
  };

  const handleRemoveChannel = async (channelId) => {
    if (window.confirm('Are you sure you want to remove this channel?')) {
      try {
        await removeChannel(channelId);
        toast.success('Channel removed successfully!');
      } catch (error) {
        toast.error(error.message || 'Failed to remove channel');
      }
    }
  };

  const handleRefreshChannel = async (channelId) => {
    try {
      await refreshChannel(channelId);
      toast.success('Channel statistics refreshed!');
    } catch (error) {
      toast.error(error.message || 'Failed to refresh channel');
    }
  };

  const handleViewChannel = (channelId) => {
    setSelectedChannel(channelId);
    // In a real app, this would navigate to a detailed view
    toast.info('Channel details view coming soon!');
  };

  return (
    <div className="dashboard">
      <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)}>
        <nav className="dashboard__nav">
          <button
            className={`dashboard__nav-item ${!showAddForm ? 'dashboard__nav-item--active' : ''}`}
            onClick={() => setShowAddForm(false)}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </svg>
            Dashboard
          </button>
          
          <button
            className={`dashboard__nav-item ${showAddForm ? 'dashboard__nav-item--active' : ''}`}
            onClick={() => setShowAddForm(true)}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            Add Channel
          </button>
        </nav>

        <div className="dashboard__stats">
          <div className="dashboard__stat">
            <span className="dashboard__stat-label">Total Channels</span>
            <span className="dashboard__stat-value">{channels.length}</span>
          </div>
          
          <div className="dashboard__stat">
            <span className="dashboard__stat-label">Active Tracking</span>
            <span className="dashboard__stat-value">
              {channels.filter(c => c.latest_statistics).length}
            </span>
          </div>
        </div>
      </Sidebar>

      <main className={`dashboard__main ${sidebarOpen ? 'dashboard__main--sidebar-open' : ''}`}>
        <div className="dashboard__content">
          {showAddForm ? (
            <div className="dashboard__add-form-container">
              <AddChannelForm
                onSubmit={handleAddChannel}
                loading={loading}
                onCancel={() => setShowAddForm(false)}
              />
            </div>
          ) : (
            <>
              <header className="dashboard__header">
                <h1 className="dashboard__title">Channel Dashboard</h1>
                <p className="dashboard__subtitle">
                  Track and monitor your YouTube channels' performance
                </p>
              </header>

              {error && (
                <div className="dashboard__error" role="alert">
                  <p>⚠️ {error.message}</p>
                  <button 
                    className="dashboard__error-retry"
                    onClick={() => window.location.reload()}
                  >
                    Retry
                  </button>
                </div>
              )}

              {loading && channels.length === 0 ? (
                <div className="dashboard__loading">
                  <div className="dashboard__spinner"></div>
                  <p>Loading channels...</p>
                </div>
              ) : channels.length === 0 ? (
                <div className="dashboard__empty">
                  <div className="dashboard__empty-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                  </div>
                  <h2 className="dashboard__empty-title">No Channels Added</h2>
                  <p className="dashboard__empty-description">
                    Start tracking YouTube channels by adding your first channel
                  </p>
                  <button
                    className="dashboard__empty-action"
                    onClick={() => setShowAddForm(true)}
                  >
                    Add Your First Channel
                  </button>
                </div>
              ) : (
                <div className="dashboard__grid">
                  {channels.map(channel => (
                    <ChannelCard
                      key={channel.id}
                      channel={channel}
                      onRemove={handleRemoveChannel}
                      onRefresh={handleRefreshChannel}
                      onView={handleViewChannel}
                    />
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </main>

      <ToastContainer
        position="bottom-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </div>
  );
};

export default Dashboard;
