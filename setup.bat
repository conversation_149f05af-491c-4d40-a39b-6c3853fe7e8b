@echo off
REM Virsnapp Setup Script for Windows
REM This script automates the initial setup of the Virsnapp application

echo 🚀 Virsnapp Setup Script for Windows
echo ======================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo ✅ Docker and Docker Compose are installed

REM Create environment files
echo 📝 Setting up environment files...

if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file
) else (
    echo ⚠️  .env file already exists
)

if not exist backend\.env (
    copy backend\.env.example backend\.env
    echo ✅ Created backend\.env file
) else (
    echo ⚠️  backend\.env file already exists
)

if not exist frontend\.env (
    copy frontend\.env.example frontend\.env
    echo ✅ Created frontend\.env file
) else (
    echo ⚠️  frontend\.env file already exists
)

REM Prompt for YouTube API key
echo.
echo 🔑 YouTube API Configuration
echo You need a YouTube Data API v3 key from Google Cloud Console
echo Visit: https://console.cloud.google.com/
echo.
set /p youtube_api_key="Enter your YouTube API key (or press Enter to skip): "

if not "%youtube_api_key%"=="" (
    REM Update .env files with YouTube API key
    powershell -Command "(Get-Content .env) -replace 'your_youtube_api_key_here', '%youtube_api_key%' | Set-Content .env -Encoding UTF8"
    powershell -Command "(Get-Content backend\.env) -replace 'your_youtube_api_key_here', '%youtube_api_key%' | Set-Content backend\.env -Encoding UTF8"
    echo ✅ YouTube API key configured
) else (
    echo ⚠️  YouTube API key skipped - you'll need to configure it manually
)

REM Generate secure keys
echo 📐 Generating secure keys...
REM Generate random strings for security
for /f "delims=" %%i in ('powershell -Command "Add-Type -AssemblyName System.Web; [System.Web.Security.Membership]::GeneratePassword(32, 8)"') do set secret_key=%%i
for /f "delims=" %%i in ('powershell -Command "Add-Type -AssemblyName System.Web; [System.Web.Security.Membership]::GeneratePassword(16, 4)"') do set postgres_password=%%i

REM Update .env files with generated keys
powershell -Command "(Get-Content .env) -replace 'your-super-secret-key-change-in-production', '%secret_key%' | Set-Content .env -Encoding UTF8"
powershell -Command "(Get-Content backend\.env) -replace 'your-super-secret-key-change-in-production', '%secret_key%' | Set-Content backend\.env -Encoding UTF8"
powershell -Command "(Get-Content .env) -replace 'virsnapp_password', '%postgres_password%' | Set-Content .env -Encoding UTF8"
powershell -Command "(Get-Content backend\.env) -replace 'virsnapp_password', '%postgres_password%' | Set-Content backend\.env -Encoding UTF8"

echo ✅ Secure keys generated

REM Build and start services
echo 🏗️  Building and starting services...
docker-compose pull
docker-compose build
docker-compose up -d

echo ✅ Services started

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak > nul

REM Run database migrations
echo 🗄️  Running database migrations...
docker-compose exec backend python -m alembic upgrade head

echo ✅ Database migrations completed

REM Display final information
echo.
echo 🎉 Virsnapp is now running!
echo ==========================
echo.
echo 📱 Frontend:     http://localhost:3000
echo 🔧 Backend API:  http://localhost:8000
echo 📚 API Docs:     http://localhost:8000/docs
echo ❤️  Health Check: http://localhost:8000/health
echo.
echo 📋 Next Steps:
echo 1. Open http://localhost:3000 in your browser
echo 2. Add your first YouTube channel
echo 3. Explore the dashboard and statistics
echo.
echo 🛠️  Useful Commands:
echo • View logs:          docker-compose logs -f
echo • Stop services:      docker-compose down
echo • Restart services:   docker-compose restart
echo • Update services:    docker-compose pull ^&^& docker-compose up -d
echo.
if "%youtube_api_key%"=="" (
    echo ⚠️  Don't forget to configure your YouTube API key!
)

pause
