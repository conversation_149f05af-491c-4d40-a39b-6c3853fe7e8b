import pytest
import asyncio
from httpx import AsyncClient
from app.main import app


@pytest.fixture
def anyio_backend():
    return "asyncio"


@pytest.mark.asyncio
async def test_health_endpoint():
    """Test the health check endpoint"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert data["version"] == "1.0.0"


@pytest.mark.asyncio
async def test_root_endpoint():
    """Test the root endpoint"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
    
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


@pytest.mark.asyncio
async def test_channels_endpoint():
    """Test the channels list endpoint"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/v1/channels")
    
    # Should return 200 even if empty
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


@pytest.mark.asyncio
async def test_invalid_endpoint():
    """Test an invalid endpoint returns 404"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/v1/nonexistent")
    
    assert response.status_code == 404
