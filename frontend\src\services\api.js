import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging and auth
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Channel API endpoints
export const channelAPI = {
  // Get all channels
  getChannels: () => api.get('/api/v1/channels'),

  // Create new channel
  createChannel: (data) => api.post('/api/v1/channels', data),

  // Get specific channel
  getChannel: (id) => api.get(`/api/v1/channels/${id}`),

  // Update channel
  updateChannel: (id, data) => api.put(`/api/v1/channels/${id}`, data),

  // Delete channel
  deleteChannel: (id) => api.delete(`/api/v1/channels/${id}`),

  // Get channel statistics
  getChannelStats: (id, params = {}) =>
    api.get(`/api/v1/channels/${id}/stats`, { params }),

  // Refresh channel statistics
  refreshChannel: (id) => api.post(`/api/v1/channels/${id}/refresh`),
};

// Health API endpoints
export const healthAPI = {
  // Health check
  getHealth: () => api.get('/health'),
};

// Error handling utility
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      message: data.detail || 'An error occurred',
      status,
      code: data.error_code || 'UNKNOWN_ERROR'
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      status: 0,
      code: 'NETWORK_ERROR'
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: 0,
      code: 'UNKNOWN_ERROR'
    };
  }
};

export default api;
