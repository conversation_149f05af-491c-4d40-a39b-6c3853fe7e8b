  
  // Get channel statistics
  getChannelStats: (id, params = {}) => 
    api.get(`/api/v1/channels/${id}/stats`, { params }),
  
  // Refresh channel statistics
  refreshChannel: (id) => api.post(`/api/v1/channels/${id}/refresh`),
};

// Health API endpoints
export const healthAPI = {
  // Health check
  getHealth: () => api.get('/health'),
};

// Error handling utility
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      message: data.detail || 'An error occurred',
      status,
      code: data.error_code || 'UNKNOWN_ERROR'
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      status: 0,
      code: 'NETWORK_ERROR'
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: 0,
      code: 'UNKNOWN_ERROR'
    };
  }
};

export default api;
