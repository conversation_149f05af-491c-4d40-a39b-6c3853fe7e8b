import asyncio
import logging
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from typing import Dict, Optional, List
from app.config import settings

logger = logging.getLogger(__name__)


class YouTubeService:
    def __init__(self):
        self.api_key = settings.youtube_api_key
        self.youtube = build('youtube', 'v3', developer<PERSON>ey=self.api_key)
    
    async def get_channel_info(self, channel_id: str) -> Optional[Dict]:
        """Get basic channel information"""
        try:
            # Run the blocking API call in a thread pool
            request = self.youtube.channels().list(
                part='snippet,statistics',
                id=channel_id
            )
            
            # Execute in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, request.execute)
            
            if not response.get('items'):
                logger.warning(f"Channel not found: {channel_id}")
                return None
            
            channel_data = response['items'][0]
            snippet = channel_data['snippet']
            statistics = channel_data['statistics']
            
            return {
                'id': channel_data['id'],
                'name': snippet['title'],
                'description': snippet.get('description', ''),
                'thumbnail_url': snippet['thumbnails'].get('high', {}).get('url'),
                'subscriber_count': int(statistics.get('subscriberCount', 0)),
                'view_count': int(statistics.get('viewCount', 0)),
                'video_count': int(statistics.get('videoCount', 0))
            }
            
        except HttpError as e:
            logger.error(f"YouTube API error for channel {channel_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching channel {channel_id}: {e}")
            return None
