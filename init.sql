-- Initialize database with proper settings
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_channels_youtube_id ON channels(youtube_id);
CREATE INDEX IF NOT EXISTS idx_statistics_channel_recorded ON statistics(channel_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_statistics_recorded_at ON statistics(recorded_at DESC);

-- Create a function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Note: The trigger will be created after the table exists via Alembic migrations
