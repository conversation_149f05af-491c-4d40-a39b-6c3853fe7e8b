@echo off
REM Virsnapp Local Development Setup Script for Windows
REM This script sets up the application for local development without Docker

echo 🚀 Virsnapp Local Development Setup for Windows
echo ===============================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 3.11+ is required but not installed.
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 18+ is required but not installed.
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

REM Check if PostgreSQL is installed
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL is required but not installed.
    echo Please install PostgreSQL from https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo ✅ All requirements are installed

REM Setup environment files
echo 📝 Setting up environment files...

if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file
) else (
    echo ⚠️  .env file already exists
)

if not exist backend\.env (
    copy backend\.env.example backend\.env
    echo ✅ Created backend\.env file
) else (
    echo ⚠️  backend\.env file already exists
)

if not exist frontend\.env (
    copy frontend\.env.example frontend\.env
    echo ✅ Created frontend\.env file
) else (
    echo ⚠️  frontend\.env file already exists
)

REM Configure database connection for local development
echo 🗄️  Configuring database connection...
powershell -Command "(gc backend\.env) -replace 'DATABASE_URL=.*', 'DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db' | Out-File -encoding ASCII backend\.env"
echo ✅ Database connection configured

REM Setup database
echo 🗄️  Setting up PostgreSQL database...
echo Please make sure PostgreSQL service is running.

REM Create database setup SQL
echo Creating database and user...
(
echo -- Create user if not exists
echo DO $$
echo BEGIN
echo    IF NOT EXISTS ^(SELECT FROM pg_catalog.pg_roles WHERE rolname = 'virsnapp_user'^) THEN
echo       CREATE USER virsnapp_user WITH PASSWORD 'virsnapp_password';
echo    END IF;
echo END
echo $$;
echo.
echo -- Create database if not exists
echo SELECT 'CREATE DATABASE virsnapp_db OWNER virsnapp_user'
echo WHERE NOT EXISTS ^(SELECT FROM pg_database WHERE datname = 'virsnapp_db'^^\gexec
echo.
echo -- Grant privileges
echo GRANT ALL PRIVILEGES ON DATABASE virsnapp_db TO virsnapp_user;
) > setup_virsnapp_db.sql

REM Execute the SQL script
psql -U postgres -f setup_virsnapp_db.sql
if %errorlevel% equ 0 (
    echo ✅ Database and user created successfully
) else (
    echo ❌ Failed to create database. Please run the SQL commands manually:
    echo psql -U postgres -f setup_virsnapp_db.sql
    echo Or follow the manual database setup in LOCAL_SETUP.md
)

del setup_virsnapp_db.sql

REM Setup backend
echo 🐍 Setting up Python backend...
cd backend

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv

REM Activate virtual environment
call venv\Scripts\activate

REM Upgrade pip
python -m pip install --upgrade pip

REM Install dependencies
echo Installing Python dependencies...
pip install -r requirements.txt

REM Run database migrations
echo Running database migrations...
python -m alembic upgrade head

echo ✅ Backend setup completed
cd ..

REM Setup frontend
echo ⚛️  Setting up React frontend...
cd frontend

REM Install dependencies
echo Installing Node.js dependencies...
npm install

echo ✅ Frontend setup completed
cd ..

REM Prompt for YouTube API key
echo.
echo 🔑 YouTube API Configuration
echo You need a YouTube Data API v3 key from Google Cloud Console
echo Visit: https://console.cloud.google.com/
echo.
set /p youtube_api_key="Enter your YouTube API key (or press Enter to skip): "

if not "%youtube_api_key%"=="" (
    REM Update backend/.env file
    powershell -Command "(gc backend\.env) -replace 'your_youtube_api_key_here', '%youtube_api_key%' | Out-File -encoding ASCII backend\.env"
    echo ✅ YouTube API key configured
) else (
    echo ⚠️  YouTube API key skipped - you can configure it later in backend\.env
)

REM Generate secure secret key
echo 🔐 Generating secure secret key...
for /f %%i in ('powershell -Command "[System.Web.Security.Membership]::GeneratePassword(32, 8)"') do set secret_key=%%i
powershell -Command "(gc backend\.env) -replace 'your-super-secret-key-change-in-development', '%secret_key%' | Out-File -encoding ASCII backend\.env"
echo ✅ Secret key generated

REM Create start scripts
echo 📜 Creating start scripts...

REM Create backend start script
(
echo @echo off
echo echo 🐍 Starting Virsnapp Backend...
echo cd backend
echo call venv\Scripts\activate
echo echo Backend starting at http://localhost:8000
echo echo API docs available at http://localhost:8000/docs
echo uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
echo pause
) > start_backend.bat

REM Create frontend start script
(
echo @echo off
echo echo ⚛️  Starting Virsnapp Frontend...
echo cd frontend
echo echo Frontend starting at http://localhost:3000
echo npm start
echo pause
) > start_frontend.bat

echo ✅ Start scripts created

REM Display final information
echo.
echo 🎉 Virsnapp local development setup completed!
echo =============================================
echo.
echo 🚀 Starting the application:
echo.
echo 1. Start the backend ^(double-click or run in terminal^):
echo    start_backend.bat
echo.
echo 2. Start the frontend ^(double-click or run in another terminal^):
echo    start_frontend.bat
echo.
echo 🌐 Access URLs:
echo • Frontend:     http://localhost:3000
echo • Backend API:  http://localhost:8000
echo • API Docs:     http://localhost:8000/docs
echo • Health Check: http://localhost:8000/health
echo.
echo 🛠️  Development Commands:
echo • Backend tests:    cd backend ^&^& venv\Scripts\activate ^&^& pytest
echo • Frontend tests:   cd frontend ^&^& npm test
echo • New migration:    cd backend ^&^& venv\Scripts\activate ^&^& python -m alembic revision --autogenerate -m "Description"
echo • Apply migration:  cd backend ^&^& venv\Scripts\activate ^&^& python -m alembic upgrade head
echo.
if "%youtube_api_key%"=="" (
    echo ⚠️  Remember to add your YouTube API key to backend\.env
)
echo Happy coding! 🚀

pause
