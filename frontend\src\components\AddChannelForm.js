        name: '',
        description: ''
      });
      setErrors({});
    } catch (error) {
      // Error handling is done by parent component
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  return (
    <form className="add-channel-form" onSubmit={handleSubmit}>
      <div className="add-channel-form__header">
        <h2 className="add-channel-form__title">Add YouTube Channel</h2>
        <p className="add-channel-form__description">
          Enter the YouTube channel details to start tracking statistics
        </p>
      </div>

      <div className="add-channel-form__fields">
        <div className="add-channel-form__field">
          <label 
            htmlFor="youtube_id" 
            className="add-channel-form__label"
          >
            YouTube Channel ID *
          </label>
          <input
            type="text"
            id="youtube_id"
            name="youtube_id"
            value={formData.youtube_id}
            onChange={handleChange}
            className={`add-channel-form__input ${errors.youtube_id ? 'add-channel-form__input--error' : ''}`}
            placeholder="UC1234567890abcdefghijk"
            disabled={loading}
            aria-describedby={errors.youtube_id ? 'youtube_id-error' : 'youtube_id-help'}
          />
          <p id="youtube_id-help" className="add-channel-form__help">
            You can find the channel ID in the channel URL or using YouTube's API
          </p>
          {errors.youtube_id && (
            <p id="youtube_id-error" className="add-channel-form__error" role="alert">
              {errors.youtube_id}
            </p>
          )}
        </div>

        <div className="add-channel-form__field">
          <label 
            htmlFor="name" 
            className="add-channel-form__label"
          >
            Channel Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`add-channel-form__input ${errors.name ? 'add-channel-form__input--error' : ''}`}
            placeholder="Enter channel name"
            disabled={loading}
            aria-describedby={errors.name ? 'name-error' : undefined}
          />
          {errors.name && (
            <p id="name-error" className="add-channel-form__error" role="alert">
              {errors.name}
            </p>
          )}
        </div>

        <div className="add-channel-form__field">
          <label 
            htmlFor="description" 
            className="add-channel-form__label"
          >
            Description (Optional)
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="add-channel-form__textarea"
            placeholder="Brief description of the channel"
            rows="3"
            disabled={loading}
          />
        </div>
      </div>

      <div className="add-channel-form__actions">
        {onCancel && (
          <button
            type="button"
            className="add-channel-form__button add-channel-form__button--secondary"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </button>
        )}
        <button
          type="submit"
          className="add-channel-form__button add-channel-form__button--primary"
          disabled={loading}
        >
          {loading ? (
            <span className="add-channel-form__loading">
              <span className="add-channel-form__spinner"></span>
              Adding Channel...
            </span>
          ) : (
            'Add Channel'
          )}
        </button>
      </div>
    </form>
  );
};

export default AddChannelForm;
