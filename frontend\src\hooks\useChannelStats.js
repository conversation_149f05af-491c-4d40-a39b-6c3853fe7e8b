import { useState, useEffect } from 'react';
import { channelAPI, handleAPIError } from '../services/api';

export const useChannelStats = (channelId, options = {}) => {
  const [stats, setStats] = useState([]);
  const [channel, setChannel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { days = 30, limit = 100, autoRefresh = false, refreshInterval = 300000 } = options;

  const fetchStats = async () => {
    if (!channelId) return;
    
    setLoading(true);
    setError(null);
    try {
      const response = await channelAPI.getChannelStats(channelId, { days, limit });
      setStats(response.data.statistics);
      setChannel(response.data.channel);
    } catch (err) {
      setError(handleAPIError(err));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [channelId, days, limit]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !channelId) return;

    const interval = setInterval(fetchStats, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, channelId]);

  return {
    stats,
    channel,
    loading,
    error,
    refetch: fetchStats
  };
};
