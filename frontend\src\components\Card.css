.card {
  background-color: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  width: 100%;
  text-align: left;
  font-family: inherit;
}

.card--hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-border-hover);
}

.card--clickable {
  cursor: pointer;
  border: none;
  color: inherit;
}

.card--clickable:hover {
  background-color: var(--color-bg-hover);
}

.card--clickable:focus {
  outline: 2px solid var(--color-accent-primary);
  outline-offset: 2px;
}

.card--padding-small {
  padding: var(--spacing-3);
}

.card--padding-large {
  padding: var(--spacing-8);
}

/* Card header component */
.card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
}

.card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  margin-top: var(--spacing-1);
}

/* Card content component */
.card__content {
  color: var(--color-text-primary);
}

/* Card footer component */
.card__footer {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Card actions */
.card__actions {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

/* Loading state */
.card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
