.App {
  height: 100vh;
  overflow: hidden;
}

/* Toast customization for dark theme */
.Toastify__toast-container {
  font-family: var(--font-family-primary);
}

.Toastify__toast {
  background-color: var(--color-bg-card);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.Toastify__toast--success {
  border-color: var(--color-success);
}

.Toastify__toast--error {
  border-color: var(--color-error);
}

.Toastify__toast--info {
  border-color: var(--color-info);
}

.Toastify__toast--warning {
  border-color: var(--color-warning);
}

.Toastify__progress-bar {
  background: var(--color-accent-primary);
}

.Toastify__close-button {
  color: var(--color-text-secondary);
}

.Toastify__close-button:hover {
  color: var(--color-text-primary);
}

/* Global focus styles */
*:focus-visible {
  outline: 2px solid var(--color-accent-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Remove focus outline for mouse users */
.js-focus-visible *:focus:not(.focus-visible) {
  outline: none;
}

/* Ensure interactive elements are large enough for touch */
@media (max-width: 768px) {
  button,
  input,
  select,
  textarea,
  [role="button"],
  [tabindex="0"] {
    min-height: 44px;
    min-width: 44px;
  }
}
