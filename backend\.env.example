# Database Configuration
DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db
POSTGRES_USER=virsnapp_user
POSTGRES_PASSWORD=virsnapp_password
POSTGRES_DB=virsnapp_db

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Application Configuration
SECRET_KEY=your-super-secret-key-change-in-production

# Environment
ENVIRONMENT=development
DEBUG=True

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
