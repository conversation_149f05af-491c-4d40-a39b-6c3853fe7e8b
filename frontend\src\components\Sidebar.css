.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background-color: var(--color-bg-secondary);
  border-right: 1px solid var(--color-border);
  transform: translateX(0);
  transition: transform var(--transition-normal);
  z-index: var(--z-index-fixed);
  display: flex;
  flex-direction: column;
}

.sidebar--closed {
  transform: translateX(-100%);
}

.sidebar--open {
  transform: translateX(0);
}

.sidebar__header {
  padding: var(--spacing-6) var(--spacing-4);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: var(--header-height);
}

.sidebar__logo {
  display: flex;
  align-items: center;
  flex: 1;
}

.sidebar__title {
  display: flex;
  flex-direction: column;
  line-height: var(--line-height-tight);
}

.sidebar__title-main {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-primary);
}

.sidebar__title-sub {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-secondary);
}

.sidebar__toggle {
  background: none;
  border: none;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.sidebar__toggle:hover {
  background-color: var(--color-bg-hover);
}

.sidebar__toggle-icon {
  width: 20px;
  height: 16px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sidebar__toggle-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--color-text-primary);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  transform-origin: center;
}

.sidebar__toggle-icon.open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.sidebar__toggle-icon.open span:nth-child(2) {
  opacity: 0;
}

.sidebar__toggle-icon.open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.sidebar__content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-4);
}

.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop);
  backdrop-filter: blur(2px);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    z-index: var(--z-index-modal);
  }
  
  .sidebar--closed {
    transform: translateX(-100%);
  }
}

/* Desktop - always show toggle */
@media (min-width: 769px) {
  .sidebar__toggle {
    display: flex;
  }
}

/* Animations for smooth transitions */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .sidebar__toggle-icon span {
    transition: none;
  }
}
