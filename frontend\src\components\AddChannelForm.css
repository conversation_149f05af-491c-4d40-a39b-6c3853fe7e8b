.add-channel-form {
  background-color: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  max-width: 500px;
  width: 100%;
}

.add-channel-form__header {
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.add-channel-form__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.add-channel-form__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.add-channel-form__fields {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.add-channel-form__field {
  display: flex;
  flex-direction: column;
}

.add-channel-form__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.add-channel-form__input,
.add-channel-form__textarea {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  font-family: inherit;
  transition: all var(--transition-fast);
  width: 100%;
}

.add-channel-form__input:focus,
.add-channel-form__textarea:focus {
  outline: none;
  border-color: var(--color-accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.add-channel-form__input:disabled,
.add-channel-form__textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.add-channel-form__input--error {
  border-color: var(--color-error);
}

.add-channel-form__input--error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.add-channel-form__textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.add-channel-form__help {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin: var(--spacing-1) 0 0 0;
  line-height: var(--line-height-normal);
}

.add-channel-form__error {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin: var(--spacing-1) 0 0 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.add-channel-form__error::before {
  content: '⚠';
  font-size: var(--font-size-sm);
}

.add-channel-form__actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
}

.add-channel-form__button {
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
}

.add-channel-form__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.add-channel-form__button--primary {
  background-color: var(--color-accent-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-accent-primary);
}

.add-channel-form__button--primary:hover:not(:disabled) {
  background-color: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  transform: translateY(-1px);
}

.add-channel-form__button--secondary {
  background-color: transparent;
  color: var(--color-text-secondary);
  border-color: var(--color-border);
}

.add-channel-form__button--secondary:hover:not(:disabled) {
  background-color: var(--color-bg-hover);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
}

.add-channel-form__loading {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.add-channel-form__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .add-channel-form {
    padding: var(--spacing-4);
  }
  
  .add-channel-form__actions {
    flex-direction: column;
  }
  
  .add-channel-form__button {
    width: 100%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .add-channel-form__input,
  .add-channel-form__textarea {
    border-width: 2px;
  }
  
  .add-channel-form__button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .add-channel-form__button,
  .add-channel-form__input,
  .add-channel-form__textarea {
    transition: none;
  }
  
  .add-channel-form__spinner {
    animation: none;
  }
}
