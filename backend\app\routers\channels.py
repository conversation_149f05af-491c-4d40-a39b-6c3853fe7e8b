from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.services.channel_service import ChannelService
from app.services.youtube_service import YouTubeService
from app.schemas import (
    Channel, ChannelCreate, ChannelUpdate, ChannelWithStats,
    ChannelStatsResponse, ErrorResponse
)
import logging

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/channels", tags=["channels"])


@router.get("/", response_model=List[Channel])
async def get_channels(db: Session = Depends(get_db)):
    """Get all tracked channels"""
    try:
        service = ChannelService(db)
        channels = service.get_all_channels()
        
        # Add latest statistics to each channel
        result = []
        for channel in channels:
            channel_dict = {
                "id": channel.id,
                "name": channel.name,
                "youtube_id": channel.youtube_id,
                "description": channel.description,
                "thumbnail_url": channel.thumbnail_url,
                "created_at": channel.created_at,
                "updated_at": channel.updated_at,
                "latest_statistics": None
            }
            
            # Get latest statistic
            if channel.statistics:
                latest_stat = max(channel.statistics, key=lambda x: x.recorded_at)
                channel_dict["latest_statistics"] = latest_stat
            
            result.append(channel_dict)
        
        return result
    except Exception as e:
        logger.error(f"Error fetching channels: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch channels"
        )


@router.post("/", response_model=Channel, status_code=status.HTTP_201_CREATED)
async def create_channel(
    channel_data: ChannelCreate,
    db: Session = Depends(get_db)
):
    """Add a new YouTube channel to track"""
    try:
        service = ChannelService(db)
        youtube_service = YouTubeService()
        
        # Check if channel already exists
        existing_channel = service.get_channel_by_youtube_id(channel_data.youtube_id)
        if existing_channel:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Channel already being tracked"
            )
        
        # Verify channel exists on YouTube and get additional info
        youtube_info = await youtube_service.get_channel_info(channel_data.youtube_id)
        if not youtube_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="YouTube channel not found"
            )
        
        # Create channel with YouTube data
        enhanced_data = ChannelCreate(
            name=youtube_info['name'],
            youtube_id=channel_data.youtube_id,
            description=youtube_info.get('description', channel_data.description)
        )
        
        channel = service.create_channel(enhanced_data)
        
        # Update channel with thumbnail
        if youtube_info.get('thumbnail_url'):
            channel.thumbnail_url = youtube_info['thumbnail_url']
            db.commit()
            db.refresh(channel)
        
        # Add initial statistics
        service.add_statistic(
            channel_id=channel.id,
            subscriber_count=youtube_info['subscriber_count'],
            view_count=youtube_info['view_count'],
            video_count=youtube_info['video_count']
        )
        
        return channel
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating channel: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create channel"
        )


@router.get("/{channel_id}", response_model=Channel)
async def get_channel(channel_id: int, db: Session = Depends(get_db)):
    """Get a specific channel by ID"""
    try:
        service = ChannelService(db)
        channel = service.get_channel_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
        return channel
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching channel {channel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch channel"
        )


@router.put("/{channel_id}", response_model=Channel)
async def update_channel(
    channel_id: int,
    channel_data: ChannelUpdate,
    db: Session = Depends(get_db)
):
    """Update channel information"""
    try:
        service = ChannelService(db)
        channel = service.update_channel(channel_id, channel_data)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
        return channel
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating channel {channel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update channel"
        )


@router.delete("/{channel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_channel(channel_id: int, db: Session = Depends(get_db)):
    """Remove a channel from tracking"""
    try:
        service = ChannelService(db)
        if not service.delete_channel(channel_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting channel {channel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete channel"
        )


@router.get("/{channel_id}/stats", response_model=ChannelStatsResponse)
async def get_channel_statistics(
    channel_id: int,
    days: int = 30,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get channel statistics for the specified time period"""
    try:
        service = ChannelService(db)
        
        # Verify channel exists
        channel = service.get_channel_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
        
        # Get statistics
        statistics = service.get_channel_statistics(channel_id, days, limit)
        
        return ChannelStatsResponse(
            channel=channel,
            statistics=statistics,
            total_count=len(statistics)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching statistics for channel {channel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch channel statistics"
        )


@router.post("/{channel_id}/refresh", response_model=Channel)
async def refresh_channel_stats(channel_id: int, db: Session = Depends(get_db)):
    """Manually refresh channel statistics from YouTube"""
    try:
        service = ChannelService(db)
        youtube_service = YouTubeService()
        
        # Verify channel exists
        channel = service.get_channel_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
        
        # Fetch latest data from YouTube
        youtube_info = await youtube_service.get_channel_info(channel.youtube_id)
        if not youtube_info:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Failed to fetch data from YouTube"
            )
        
        # Add new statistics
        service.add_statistic(
            channel_id=channel.id,
            subscriber_count=youtube_info['subscriber_count'],
            view_count=youtube_info['view_count'],
            video_count=youtube_info['video_count']
        )
        
        # Update channel info if needed
        if youtube_info.get('thumbnail_url') and channel.thumbnail_url != youtube_info['thumbnail_url']:
            channel.thumbnail_url = youtube_info['thumbnail_url']
            db.commit()
            db.refresh(channel)
        
        return channel
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing channel {channel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh channel statistics"
        )
