from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # Database
    database_url: str

    # YouTube API
    youtube_api_key: str

    # Security
    secret_key: str

    # Environment
    environment: str = "development"
    debug: bool = True

    # CORS
    allowed_origins: List[str] = ["http://localhost:3000"]

    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
