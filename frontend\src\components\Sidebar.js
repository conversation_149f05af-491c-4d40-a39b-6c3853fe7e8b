import React, { useState, useRef, useEffect } from 'react';
import './Sidebar.css';

const Sidebar = ({ isOpen, onToggle, children }) => {
  const sidebarRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isMobile &&
        isOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target)
      ) {
        onToggle();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, isMobile, onToggle]);

  return (
    <>
      {/* Mobile backdrop */}
      {isMobile && isOpen && (
        <div 
          className="sidebar-backdrop"
          onClick={onToggle}
          aria-hidden="true"
        />
      )}
      
      {/* Sidebar */}
      <aside
        ref={sidebarRef}
        className={`sidebar ${isOpen ? 'sidebar--open' : 'sidebar--closed'}`}
        aria-label="Navigation sidebar"
      >
        <div className="sidebar__header">
          <div className="sidebar__logo">
            <h1 className="sidebar__title">
              <span className="sidebar__title-main">Virsnapp</span>
              <span className="sidebar__title-sub">YouTube Tracker</span>
            </h1>
          </div>
          
          <button
            className="sidebar__toggle"
            onClick={onToggle}
            aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
            aria-expanded={isOpen}
          >
            <span className={`sidebar__toggle-icon ${isOpen ? 'open' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>

        <div className="sidebar__content">
          {children}
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
