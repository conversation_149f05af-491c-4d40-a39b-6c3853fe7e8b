# Local Development with PostgreSQL in Docker

This setup allows you to run the backend and frontend locally while using PostgreSQL in a Docker container. This is ideal for development as it provides database isolation without the complexity of running the entire application in Docker.

## Prerequisites

- **Python 3.11+** installed
- **Node.js 18+** and npm installed
- **Docker** installed (only for PostgreSQL)
- **Git** for version control

## 🐳 Database Setup (Docker)

### 1. PostgreSQL Docker Compose
Create a minimal Docker Compose file just for PostgreSQL:

```yaml
# docker-compose.db.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: virsnapp_postgres_local
    environment:
      POSTGRES_USER: virsnapp_user
      POSTGRES_PASSWORD: virsnapp_password
      POSTGRES_DB: virsnapp_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U virsnapp_user"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_local_data:
```

### 2. Start PostgreSQL Container
```bash
# Start PostgreSQL in Docker
docker-compose -f docker-compose.db.yml up -d

# Check if it's running
docker-compose -f docker-compose.db.yml ps

# View logs
docker-compose -f docker-compose.db.yml logs -f postgres
```

### 3. Connect to Database (Optional)
```bash
# Connect to the database
docker exec -it virsnapp_postgres_local psql -U virsnapp_user -d virsnapp_db

# Or using local psql if installed
psql -h localhost -U virsnapp_user -d virsnapp_db
```

## 🚀 Quick Start

### Automated Setup (Recommended)

**Linux/macOS:**
```bash
# Make setup script executable
chmod +x setup_local_with_docker_db.sh

# Run setup
./setup_local_with_docker_db.sh
```

**Windows:**
```cmd
# Run setup script
setup_local_with_docker_db.bat
```

### Manual Setup

1. **Start PostgreSQL in Docker:**
```bash
docker-compose -f docker-compose.db.yml up -d
```

2. **Setup Environment Files:**
```bash
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

3. **Configure Backend Environment:**
Edit `backend/.env`:
```bash
DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db
YOUTUBE_API_KEY=your_youtube_api_key_here
SECRET_KEY=your-super-secret-key-change-in-development
DEBUG=True
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

4. **Setup Backend:**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python -m alembic upgrade head
```

5. **Setup Frontend:**
```bash
cd frontend
npm install
```

## 🏃‍♂️ Running the Application

### Start All Services

**Option 1: Use Start Scripts**
```bash
# Terminal 1: Start database (if not running)
docker-compose -f docker-compose.db.yml up -d

# Terminal 2: Start backend
./start_backend.sh  # or start_backend.bat on Windows

# Terminal 3: Start frontend
./start_frontend.sh  # or start_frontend.bat on Windows
```

**Option 2: Manual Start**
```bash
# Terminal 1: Database
docker-compose -f docker-compose.db.yml up -d

# Terminal 2: Backend
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 3: Frontend
cd frontend
npm start
```

## 🌐 Access URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Database**: localhost:5432 (virsnapp_user/virsnapp_password)

## 🛠 Development Commands

### Database Management
```bash
# Start database
docker-compose -f docker-compose.db.yml up -d

# Stop database
docker-compose -f docker-compose.db.yml down

# View database logs
docker-compose -f docker-compose.db.yml logs -f postgres

# Connect to database
docker exec -it virsnapp_postgres_local psql -U virsnapp_user -d virsnapp_db

# Backup database
docker exec virsnapp_postgres_local pg_dump -U virsnapp_user virsnapp_db > backup.sql

# Restore database
docker exec -i virsnapp_postgres_local psql -U virsnapp_user virsnapp_db < backup.sql
```

### Backend Development
```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate

# Start development server
uvicorn app.main:app --reload

# Create migration
python -m alembic revision --autogenerate -m "Description"

# Apply migrations
python -m alembic upgrade head

# Run tests
pytest

# Format code
black .
isort .
```

### Frontend Development
```bash
cd frontend

# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Install new package
npm install package-name
```

## 🔧 Troubleshooting

### Database Issues
```bash
# Check if container is running
docker ps | grep virsnapp_postgres_local

# Check container logs
docker logs virsnapp_postgres_local

# Restart database container
docker-compose -f docker-compose.db.yml restart postgres

# Remove and recreate database
docker-compose -f docker-compose.db.yml down -v
docker-compose -f docker-compose.db.yml up -d
```

### Connection Issues
```bash
# Test database connection
cd backend
source venv/bin/activate
python -c "from app.database import engine; print('Connection successful!' if engine.connect() else 'Connection failed!')"

# Check if port 5432 is available
netstat -an | grep 5432  # Linux/macOS
netstat -an | findstr 5432  # Windows
```

### Backend Issues
```bash
# Check if virtual environment is activated
which python  # Should point to venv/bin/python

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check environment variables
python -c "from app.config import settings; print(f'DB: {settings.database_url}')"
```

### Frontend Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check environment variables
echo $REACT_APP_API_URL  # Should show http://localhost:8000
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
pytest -v
pytest --cov=app --cov-report=html
```

### Frontend Testing
```bash
cd frontend
npm test
npm test -- --coverage --watchAll=false
```

### Integration Testing
```bash
# Test API endpoints
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/channels

# Test frontend
curl http://localhost:3000
```

## 📊 Monitoring

### View Logs
```bash
# Database logs
docker-compose -f docker-compose.db.yml logs -f postgres

# Backend logs (in terminal where uvicorn is running)
# Frontend logs (in browser console)
```

### Database Monitoring
```bash
# Connect to database
docker exec -it virsnapp_postgres_local psql -U virsnapp_user -d virsnapp_db

# Check database size
SELECT pg_size_pretty(pg_database_size('virsnapp_db'));

# View active connections
SELECT * FROM pg_stat_activity WHERE datname = 'virsnapp_db';

# Check table sizes
SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size 
FROM pg_tables WHERE schemaname NOT IN ('information_schema','pg_catalog');
```

## 🔄 Workflow

### Daily Development
1. Start database: `docker-compose -f docker-compose.db.yml up -d`
2. Start backend: `./start_backend.sh`
3. Start frontend: `./start_frontend.sh`
4. Develop and test
5. Stop services when done

### Adding New Features
1. Create database migration if needed
2. Add backend logic (models, services, routes)
3. Add frontend components
4. Test locally
5. Commit changes

### Environment Cleanup
```bash
# Stop all services
docker-compose -f docker-compose.db.yml down

# Remove database data (careful!)
docker-compose -f docker-compose.db.yml down -v

# Clean up Docker
docker system prune
```

This setup provides the best of both worlds: local development flexibility with reliable database containerization.
