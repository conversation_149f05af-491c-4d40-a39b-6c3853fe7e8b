version: '3.8'

services:
  # PostgreSQL Database Only
  postgres:
    image: postgres:15-alpine
    container_name: virsnapp_postgres_local
    environment:
      POSTGRES_USER: virsnapp_user
      POSTGRES_PASSWORD: virsnapp_password
      POSTGRES_DB: virsnapp_db
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U virsnapp_user -d virsnapp_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - virsnapp_local_network

  # Optional: Redis for future features
  redis:
    image: redis:7-alpine
    container_name: virsnapp_redis_local
    ports:
      - "6379:6379"
    volumes:
      - redis_local_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - virsnapp_local_network
    command: redis-server --appendonly yes

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: virsnapp_pgadmin_local
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_local_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - virsnapp_local_network

volumes:
  postgres_local_data:
    driver: local
  redis_local_data:
    driver: local
  pgadmin_local_data:
    driver: local

networks:
  virsnapp_local_network:
    driver: bridge
