import React from 'react';
import './Card.css';

const Card = ({ 
  children, 
  className = '', 
  onClick,
  hover = false,
  padding = 'normal',
  ...props 
}) => {
  const cardClasses = [
    'card',
    hover && 'card--hover',
    padding === 'large' && 'card--padding-large',
    padding === 'small' && 'card--padding-small',
    onClick && 'card--clickable',
    className
  ].filter(Boolean).join(' ');

  const CardComponent = onClick ? 'button' : 'div';

  return (
    <CardComponent
      className={cardClasses}
      onClick={onClick}
      type={onClick ? 'button' : undefined}
      {...props}
    >
      {children}
    </CardComponent>
  );
};

export default Card;
