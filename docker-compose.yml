version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: virsnapp_postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-virsnapp_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-virsnapp_password}
      POSTGRES_DB: ${POSTGRES_DB:-virsnapp_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - virsnapp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-virsnapp_user}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: virsnapp_redis
    ports:
      - "6379:6379"
    networks:
      - virsnapp_network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: virsnapp_backend
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-virsnapp_user}:${POSTGRES_PASSWORD:-virsnapp_password}@postgres:5432/${POSTGRES_DB:-virsnapp_db}
      REDIS_URL: redis://redis:6379
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key}
      ENVIRONMENT: ${ENVIRONMENT:-development}
      DEBUG: ${DEBUG:-True}
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS:-http://localhost:3000}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - virsnapp_network
    volumes:
      - ./backend:/app
    command: >
      sh -c "
        python -m alembic upgrade head &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: virsnapp_frontend
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:8000}
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - virsnapp_network
    volumes:
      - ./frontend:/app
      - /app/node_modules

volumes:
  postgres_data:

networks:
  virsnapp_network:
    driver: bridge
