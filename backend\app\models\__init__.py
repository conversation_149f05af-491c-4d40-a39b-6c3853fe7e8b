from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class Channel(Base):
    __tablename__ = "channels"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    youtube_id = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(String(1000), nullable=True)
    thumbnail_url = Column(String(500), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationship to statistics
    statistics = relationship("Statistic", back_populates="channel", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Channel(id={self.id}, name='{self.name}', youtube_id='{self.youtube_id}')>"


class Statistic(Base):
    __tablename__ = "statistics"
    
    id = Column(Integer, primary_key=True, index=True)
    channel_id = Column(Integer, ForeignKey("channels.id"), nullable=False)
    subscriber_count = Column(BigInteger, nullable=False, default=0)
    view_count = Column(BigInteger, nullable=False, default=0)
    video_count = Column(Integer, nullable=False, default=0)
    recorded_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Relationship to channel
    channel = relationship("Channel", back_populates="statistics")
    
    def __repr__(self):
        return f"<Statistic(id={self.id}, channel_id={self.channel_id}, subscribers={self.subscriber_count})>"


# Create indexes for better performance
Index('idx_statistics_channel_recorded', Statistic.channel_id, Statistic.recorded_at)
Index('idx_channels_youtube_id', Channel.youtube_id)
