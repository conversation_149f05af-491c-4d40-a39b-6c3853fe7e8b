from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime
from app.database import get_db
from app.schemas import HealthResponse
from app.services.youtube_service import YouTubeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["health"])


@router.get("/health", response_model=HealthResponse)
async def health_check(db: Session = Depends(get_db)):
    """Health check endpoint"""
    try:
        # Check database connection
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = "unhealthy"
    
    # Check YouTube API
    try:
        youtube_service = YouTubeService()
        # This is a minimal check - just creating the service
        # For a real check, you might want to make a test API call
        youtube_status = "healthy"
    except Exception as e:
        logger.error(f"YouTube API health check failed: {e}")
        youtube_status = "unhealthy"
    
    # Overall status
    overall_status = "healthy" if db_status == "healthy" and youtube_status == "healthy" else "unhealthy"
    
    return HealthResponse(
        status=overall_status,
        timestamp=datetime.utcnow(),
        database=db_status,
        youtube_api=youtube_status
    )


@router.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Virsnapp YouTube Channel Tracker API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }
