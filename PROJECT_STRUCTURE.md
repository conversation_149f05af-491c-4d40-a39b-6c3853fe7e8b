│   │   │
│   │   ├── 📁 services/             # Business logic
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 channel_service.py    # Channel operations
│   │   │   └── 📄 youtube_service.py    # YouTube API integration
│   │   │
│   │   └── 📁 routers/              # API endpoints
│   │       ├── 📄 __init__.py
│   │       ├── 📄 channels.py       # Channel API routes
│   │       └── 📄 health.py         # Health check routes
│   │
│   ├── 📁 migrations/               # Database migrations
│   │   ├── 📄 env.py                # Alembic environment
│   │   ├── 📄 script.py.mako        # Migration template
│   │   └── 📁 versions/             # Migration files
│   │
│   └── 📁 tests/                    # Backend tests
│       ├── 📄 __init__.py
│       └── 📄 test_main.py          # API endpoint tests
│
└── 📁 frontend/                     # React Frontend
    ├── 📄 package.json              # Node.js dependencies
    ├── 📄 Dockerfile                # Development Docker image
    ├── 📄 Dockerfile.prod           # Production Docker image
    ├── 📄 nginx.conf                # Frontend nginx config
    ├── 📄 .env.example              # Environment variables template
    │
    ├── 📁 public/                   # Static assets
    │   ├── 📄 index.html            # Main HTML template
    │   └── 📄 manifest.json         # PWA manifest
    │
    └── 📁 src/                      # Source code
        ├── 📄 index.js              # React app entry point
        ├── 📄 App.js                # Main App component
        ├── 📄 App.css               # Global app styles
        ├── 📄 Dashboard.js          # Main dashboard component
        ├── 📄 Dashboard.css         # Dashboard styles
        │
        ├── 📁 components/           # Reusable components
        │   ├── 📄 Sidebar.js        # Navigation sidebar
        │   ├── 📄 Sidebar.css       # Sidebar styles
        │   ├── 📄 Card.js           # Generic card component
        │   ├── 📄 Card.css          # Card styles
        │   ├── 📄 ChannelCard.js    # Channel display card
        │   ├── 📄 ChannelCard.css   # Channel card styles
        │   ├── 📄 AddChannelForm.js # Add channel form
        │   ├── 📄 AddChannelForm.css # Form styles
        │   ├── 📄 StatsChart.js     # Statistics chart
        │   └── 📄 StatsChart.css    # Chart styles
        │
        ├── 📁 hooks/                # Custom React hooks
        │   ├── 📄 useChannels.js    # Channel data management
        │   └── 📄 useChannelStats.js # Statistics data management
        │
        ├── 📁 services/             # API services
        │   └── 📄 api.js            # HTTP client and API calls
        │
        └── 📁 styles/               # Global styles
            └── 📄 variables.css     # CSS custom properties
```

## 🚀 Quick Start Commands

### Development Environment
```bash
# Clone and setup
git clone <repository-url>
cd virsnapp

# Automated setup (Linux/macOS)
chmod +x setup.sh
./setup.sh

# Automated setup (Windows)
setup.bat

# Manual setup
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit environment files with your YouTube API key
nano .env

# Start services
docker-compose up -d

# View logs
docker-compose logs -f
```

### Production Deployment
```bash
# Setup production environment
cp .env.example .env.prod
nano .env.prod  # Configure production settings

# Deploy with production compose
docker-compose -f docker-compose.prod.yml up -d

# Monitor logs
docker-compose -f docker-compose.prod.yml logs -f
```

## 🔧 Development Commands

### Backend Development
```bash
cd backend

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn app.main:app --reload

# Create database migration
python -m alembic revision --autogenerate -m "Description"

# Apply migrations
python -m alembic upgrade head

# Run tests
pytest
```

### Frontend Development
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test
```

### Docker Commands
```bash
# Build images
docker-compose build

# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f [service-name]

# Execute commands in containers
docker-compose exec backend bash
docker-compose exec frontend sh

# Scale services
docker-compose up -d --scale backend=3
```

## 🌐 Access URLs

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Interactive API Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## 📋 Key Features Implemented

### ✅ Backend Features
- FastAPI with async/await support
- PostgreSQL database with SQLAlchemy ORM
- YouTube Data API v3 integration
- Database migrations with Alembic
- Comprehensive error handling
- API documentation with OpenAPI/Swagger
- Health checks and monitoring
- Rate limiting and security headers
- Docker containerization

### ✅ Frontend Features
- React 18+ with modern hooks
- Dark theme design system
- Responsive mobile-first design
- Real-time data visualization with Chart.js
- Interactive dashboard with statistics
- Form validation and error handling
- Loading states and user feedback
- Accessibility compliance (WCAG 2.1 AA)
- Progressive Web App features

### ✅ DevOps Features
- Multi-stage Docker builds
- Development and production environments
- Nginx reverse proxy
- SSL/HTTPS ready configuration
- Database connection pooling
- Automated setup scripts
- Comprehensive documentation
- Testing framework setup

## 🔐 Security Features

- Environment variable configuration
- Secure secret key generation
- CORS protection
- SQL injection prevention
- XSS protection headers
- Rate limiting
- Input validation and sanitization
- Error handling without information leakage

## 📊 Performance Optimizations

- Database indexing on frequently queried columns
- Connection pooling for database
- Nginx gzip compression
- Static asset caching
- Lazy loading for React components
- Optimized Docker images
- Health checks for all services

## 🧪 Testing Setup

- Pytest for backend testing
- Jest for frontend testing
- Docker test environments
- API endpoint testing
- Component testing setup
- Coverage reporting

This project provides a complete, production-ready YouTube channel statistics tracking application with modern architecture, comprehensive documentation, and automated deployment capabilities.
