from pydantic import BaseModel, field_validator
from datetime import datetime
from typing import Optional, List


class ChannelBase(BaseModel):
    name: str
    youtube_id: str
    description: Optional[str] = None


class ChannelCreate(ChannelBase):
    @field_validator('youtube_id')
    @classmethod
    def validate_youtube_id(cls, v):
        if not v or len(v) < 10:
            raise ValueError('Invalid YouTube channel ID')
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('Channel name cannot be empty')
        return v.strip()


class ChannelUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None


class StatisticBase(BaseModel):
    subscriber_count: int
    view_count: int
    video_count: int


class StatisticCreate(StatisticBase):
    channel_id: int


class Statistic(StatisticBase):
    id: int
    channel_id: int
    recorded_at: datetime
    
    class Config:
        from_attributes = True


class Channel(ChannelBase):
    id: int
    thumbnail_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    latest_statistics: Optional[Statistic] = None
    
    class Config:
        from_attributes = True


class ChannelWithStats(Channel):
    statistics: List[Statistic] = []


class ChannelStatsResponse(BaseModel):
    channel: Channel
    statistics: List[Statistic]
    total_count: int


class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None


class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"
    database: str
    youtube_api: str
