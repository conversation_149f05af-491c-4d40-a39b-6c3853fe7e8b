from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, and_
from typing import List, Optional
from datetime import datetime, timedelta
from app.models import Channel, Statistic
from app.schemas import ChannelCreate, ChannelUpdate
import logging

logger = logging.getLogger(__name__)


class ChannelService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_all_channels(self) -> List[Channel]:
        """Get all channels with their latest statistics"""
        return (
            self.db.query(Channel)
            .options(joinedload(Channel.statistics))
            .all()
        )
    
    def get_channel_by_id(self, channel_id: int) -> Optional[Channel]:
        """Get channel by ID"""
        return (
            self.db.query(Channel)
            .options(joinedload(Channel.statistics))
            .filter(Channel.id == channel_id)
            .first()
        )
    
    def get_channel_by_youtube_id(self, youtube_id: str) -> Optional[Channel]:
        """Get channel by YouTube ID"""
        return (
            self.db.query(Channel)
            .filter(Channel.youtube_id == youtube_id)
            .first()
        )
    
    def create_channel(self, channel_data: ChannelCreate) -> Channel:
        """Create a new channel"""
        db_channel = Channel(
            name=channel_data.name,
            youtube_id=channel_data.youtube_id,
            description=channel_data.description
        )
        self.db.add(db_channel)
        self.db.commit()
        self.db.refresh(db_channel)
        return db_channel
    
    def update_channel(self, channel_id: int, channel_data: ChannelUpdate) -> Optional[Channel]:
        """Update channel information"""
        db_channel = self.get_channel_by_id(channel_id)
        if not db_channel:
            return None
        
        update_data = channel_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_channel, field, value)
        
        self.db.commit()
        self.db.refresh(db_channel)
        return db_channel
    
    def delete_channel(self, channel_id: int) -> bool:
        """Delete a channel and all its statistics"""
        db_channel = self.get_channel_by_id(channel_id)
        if not db_channel:
            return False
        
        self.db.delete(db_channel)
        self.db.commit()
        return True
    
    def get_channel_statistics(
        self, 
        channel_id: int, 
        days: int = 30,
        limit: int = 100
    ) -> List[Statistic]:
        """Get channel statistics for the last N days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        return (
            self.db.query(Statistic)
            .filter(
                and_(
                    Statistic.channel_id == channel_id,
                    Statistic.recorded_at >= cutoff_date
                )
            )
            .order_by(desc(Statistic.recorded_at))
            .limit(limit)
            .all()
        )
    
    def add_statistic(
        self,
        channel_id: int,
        subscriber_count: int,
        view_count: int,
        video_count: int
    ) -> Statistic:
        """Add new statistics for a channel"""
        db_statistic = Statistic(
            channel_id=channel_id,
            subscriber_count=subscriber_count,
            view_count=view_count,
            video_count=video_count
        )
        self.db.add(db_statistic)
        self.db.commit()
        self.db.refresh(db_statistic)
        return db_statistic
