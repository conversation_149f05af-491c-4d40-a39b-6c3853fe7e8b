import { useState, useEffect } from 'react';
import { channelAPI, handleAPIError } from '../services/api';

export const useChannels = () => {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchChannels = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await channelAPI.getChannels();
      setChannels(response.data);
    } catch (err) {
      setError(handleAPIError(err));
    } finally {
      setLoading(false);
    }
  };

  const addChannel = async (channelData) => {
    setLoading(true);
    setError(null);
    try {
      const response = await channelAPI.createChannel(channelData);
      setChannels(prev => [...prev, response.data]);
      return response.data;
    } catch (err) {
      const errorInfo = handleAPIError(err);
      setError(errorInfo);
      throw errorInfo;
    } finally {
      setLoading(false);
    }
  };

  const removeChannel = async (channelId) => {
    setLoading(true);
    setError(null);
    try {
      await channelAPI.deleteChannel(channelId);
      setChannels(prev => prev.filter(channel => channel.id !== channelId));
    } catch (err) {
      const errorInfo = handleAPIError(err);
      setError(errorInfo);
      throw errorInfo;
    } finally {
      setLoading(false);
    }
  };

  const refreshChannel = async (channelId) => {
    setError(null);
    try {
      const response = await channelAPI.refreshChannel(channelId);
      setChannels(prev => 
        prev.map(channel => 
          channel.id === channelId ? response.data : channel
        )
      );
      return response.data;
    } catch (err) {
      const errorInfo = handleAPIError(err);
      setError(errorInfo);
      throw errorInfo;
    }
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  return {
    channels,
    loading,
    error,
    fetchChannels,
    addChannel,
    removeChannel,
    refreshChannel
  };
};
