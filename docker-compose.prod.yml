# Production Docker Compose
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: virsnapp_postgres_prod
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - virsnapp_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: virsnapp_redis_prod
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - virsnapp_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: virsnapp_backend_prod
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      REDIS_URL: redis://redis:6379
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      SECRET_KEY: ${SECRET_KEY}
      ENVIRONMENT: production
      DEBUG: "False"
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - virsnapp_network
    restart: unless-stopped
    command: >
      sh -c "
        python -m alembic upgrade head &&
        gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
      "

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: virsnapp_frontend_prod
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL}
    depends_on:
      - backend
    networks:
      - virsnapp_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: virsnapp_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - virsnapp_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  virsnapp_network:
    driver: bridge
