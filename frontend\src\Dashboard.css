error-retry:hover {
  opacity: 0.9;
}

/* Loading state */
.dashboard__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16);
  text-align: center;
}

.dashboard__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

.dashboard__loading p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.dashboard__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16);
  text-align: center;
  background-color: var(--color-bg-card);
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-xl);
}

.dashboard__empty-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.dashboard__empty-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.dashboard__empty-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--spacing-6) 0;
  max-width: 400px;
}

.dashboard__empty-action {
  background-color: var(--color-accent-primary);
  color: var(--color-text-inverse);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dashboard__empty-action:hover {
  background-color: var(--color-accent-hover);
  transform: translateY(-1px);
}

/* Channel grid */
.dashboard__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

/* Add form container */
.dashboard__add-form-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
  padding: var(--spacing-8) 0;
}

/* Responsive design */
@media (max-width: 1024px) {
  .dashboard__grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .dashboard__main--sidebar-open {
    margin-left: 0;
  }
  
  .dashboard__content {
    padding: var(--spacing-4);
  }
  
  .dashboard__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .dashboard__header {
    margin-bottom: var(--spacing-6);
  }
  
  .dashboard__title {
    font-size: var(--font-size-2xl);
  }
  
  .dashboard__subtitle {
    font-size: var(--font-size-base);
  }
  
  .dashboard__empty {
    padding: var(--spacing-8);
  }
  
  .dashboard__empty-title {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .dashboard__content {
    padding: var(--spacing-3);
  }
  
  .dashboard__error {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }
  
  .dashboard__add-form-container {
    padding: var(--spacing-4) 0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .dashboard__empty {
    border-width: 3px;
  }
  
  .dashboard__nav-item {
    border: 1px solid transparent;
  }
  
  .dashboard__nav-item--active {
    border-color: var(--color-text-inverse);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .dashboard__main,
  .dashboard__nav-item,
  .dashboard__empty-action,
  .dashboard__spinner {
    transition: none;
    animation: none;
  }
}

/* Print styles */
@media print {
  .dashboard__nav,
  .dashboard__stats,
  .dashboard__error-retry,
  .dashboard__empty-action {
    display: none;
  }
  
  .dashboard__main--sidebar-open {
    margin-left: 0;
  }
  
  .dashboard__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
