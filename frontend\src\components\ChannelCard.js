import React from 'react';
import Card from './Card';
import './ChannelCard.css';

const ChannelCard = ({ channel, onRemove, onRefresh, onView }) => {
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toLocaleString() || '0';
  };

  const getLatestStats = () => {
    return channel.latest_statistics || {
      subscriber_count: 0,
      view_count: 0,
      video_count: 0
    };
  };

  const stats = getLatestStats();

  return (
    <Card className="channel-card" hover>
      <div className="channel-card__header">
        <div className="channel-card__info">
          {channel.thumbnail_url && (
            <img
              src={channel.thumbnail_url}
              alt={`${channel.name} thumbnail`}
              className="channel-card__thumbnail"
              loading="lazy"
            />
          )}
          <div className="channel-card__details">
            <h3 className="channel-card__name">{channel.name}</h3>
            <p className="channel-card__id">ID: {channel.youtube_id}</p>
            {channel.description && (
              <p className="channel-card__description">
                {channel.description.slice(0, 100)}
                {channel.description.length > 100 && '...'}
              </p>
            )}
          </div>
        </div>
        
        <div className="channel-card__actions">
          <button
            className="channel-card__action channel-card__action--view"
            onClick={() => onView(channel.id)}
            aria-label={`View ${channel.name} details`}
            title="View Details"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
            </svg>
          </button>
          
          <button
            className="channel-card__action channel-card__action--refresh"
            onClick={() => onRefresh(channel.id)}
            aria-label={`Refresh ${channel.name} statistics`}
            title="Refresh Statistics"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
          </button>
          
          <button
            className="channel-card__action channel-card__action--remove"
            onClick={() => onRemove(channel.id)}
            aria-label={`Remove ${channel.name} from tracking`}
            title="Remove Channel"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>

      <div className="channel-card__stats">
        <div className="channel-card__stat">
          <span className="channel-card__stat-label">Subscribers</span>
          <span className="channel-card__stat-value">
            {formatNumber(stats.subscriber_count)}
          </span>
        </div>
        
        <div className="channel-card__stat">
          <span className="channel-card__stat-label">Views</span>
          <span className="channel-card__stat-value">
            {formatNumber(stats.view_count)}
          </span>
        </div>
        
        <div className="channel-card__stat">
          <span className="channel-card__stat-label">Videos</span>
          <span className="channel-card__stat-value">
            {stats.video_count?.toLocaleString() || '0'}
          </span>
        </div>
      </div>

      {channel.latest_statistics && (
        <div className="channel-card__footer">
          <span className="channel-card__last-updated">
            Last updated: {new Date(channel.latest_statistics.recorded_at).toLocaleDateString()}
          </span>
        </div>
      )}
    </Card>
  );
};

export default ChannelCard;
