.stats-chart {
  background-color: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  position: relative;
}

.stats-chart--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.stats-chart__empty-message {
  text-align: center;
  color: var(--color-text-secondary);
}

.stats-chart__empty-message p {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
}

.stats-chart__empty-subtitle {
  font-size: var(--font-size-sm) !important;
  color: var(--color-text-muted) !important;
  margin-top: var(--spacing-2) !important;
}

/* Chart container styling */
.stats-chart canvas {
  border-radius: var(--radius-md);
}

/* Loading overlay */
.stats-chart--loading {
  position: relative;
  opacity: 0.7;
}

.stats-chart--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(26, 26, 26, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  z-index: 10;
}

.stats-chart--loading::before {
  content: 'Loading...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  z-index: 11;
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-chart {
    padding: var(--spacing-3);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stats-chart {
    border-width: 2px;
  }
}
