import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';
import './StatsChart.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

const StatsChart = ({ 
  data, 
  title = "Channel Statistics", 
  metric = "subscriber_count",
  height = 400 
}) => {
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toLocaleString() || '0';
  };

  const getMetricLabel = (metric) => {
    switch (metric) {
      case 'subscriber_count':
        return 'Subscribers';
      case 'view_count':
        return 'Views';
      case 'video_count':
        return 'Videos';
      default:
        return 'Count';
    }
  };

  const getMetricColor = (metric) => {
    switch (metric) {
      case 'subscriber_count':
        return 'rgb(0, 255, 136)';
      case 'view_count':
        return 'rgb(59, 130, 246)';
      case 'video_count':
        return 'rgb(245, 158, 11)';
      default:
        return 'rgb(0, 255, 136)';
    }
  };

  // Sort data by date and prepare chart data
  const sortedData = [...data].sort((a, b) => 
    new Date(a.recorded_at) - new Date(b.recorded_at)
  );

  const chartData = {
    labels: sortedData.map(item => new Date(item.recorded_at)),
    datasets: [
      {
        label: getMetricLabel(metric),
        data: sortedData.map(item => item[metric]),
        borderColor: getMetricColor(metric),
        backgroundColor: getMetricColor(metric) + '20',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: getMetricColor(metric),
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#ffffff',
          font: {
            size: 14,
          },
        },
      },
      title: {
        display: true,
        text: title,
        color: '#ffffff',
        font: {
          size: 16,
          weight: 'bold',
        },
      },
      tooltip: {
        backgroundColor: '#2d2d2d',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#404040',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatNumber(context.parsed.y)}`;
          },
          title: function(context) {
            return new Date(context[0].parsed.x).toLocaleDateString();
          },
        },
      },
    },
    scales: {
      x: {
        type: 'time',
        time: {
          displayFormats: {
            day: 'MMM dd',
            week: 'MMM dd',
            month: 'MMM yyyy'
          }
        },
        grid: {
          color: '#404040',
        },
        ticks: {
          color: '#b3b3b3',
          maxTicksLimit: 8,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: '#404040',
        },
        ticks: {
          color: '#b3b3b3',
          callback: function(value) {
            return formatNumber(value);
          },
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
    elements: {
      point: {
        hoverBackgroundColor: '#ffffff',
      },
    },
  };

  if (!data || data.length === 0) {
    return (
      <div className="stats-chart stats-chart--empty">
        <div className="stats-chart__empty-message">
          <p>No data available</p>
          <p className="stats-chart__empty-subtitle">
            Statistics will appear here once data is collected
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="stats-chart" style={{ height }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default StatsChart;
