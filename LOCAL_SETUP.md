# Local Development Setup (Without Docker)

This guide will help you run Virsnap<PERSON> locally without <PERSON><PERSON> for development purposes.

## Prerequisites

- **Python 3.11+** installed
- **Node.js 18+** and npm installed
- **PostgreSQL 12+** installed and running
- **Redis** installed and running (optional, for future features)
- **Git** for version control

## 🔧 Initial Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Virsnapp
```

### 2. Database Setup

#### Install PostgreSQL
- **Windows**: Download from https://www.postgresql.org/download/windows/
- **macOS**: `brew install postgresql`
- **Linux**: `sudo apt-get install postgresql postgresql-contrib`

#### Create Database and User
```bash
# Start PostgreSQL service
# Windows: Start from Services or pgAdmin
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql

# Connect to PostgreSQL
psql -U postgres

# Create database and user
CREATE USER virsnapp_user WITH PASSWORD 'virsnapp_password';
CREATE DATABASE virsnapp_db OWNER virsnapp_user;
GRANT ALL PRIVILEGES ON DATABASE virsnapp_db TO virsnapp_user;
\q
```

### 3. Environment Configuration
```bash
# Copy environment files
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

### 4. Configure Environment Variables

Edit `backend/.env`:
```bash
# Database Configuration
DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Application Configuration
SECRET_KEY=your-super-secret-key-change-in-development
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Environment
ENVIRONMENT=development
DEBUG=True

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

Edit `frontend/.env`:
```bash
REACT_APP_API_URL=http://localhost:8000
GENERATE_SOURCEMAP=true
BROWSER=none
```

## 🐍 Backend Setup

### 1. Navigate to Backend Directory
```bash
cd backend
```

### 2. Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate

# macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Run Database Migrations
```bash
# Initialize Alembic (if needed)
python -m alembic init migrations

# Create initial migration
python -m alembic revision --autogenerate -m "Initial migration"

# Apply migrations
python -m alembic upgrade head
```

### 5. Start Backend Server
```bash
# Development server with auto-reload
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or using Python directly
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The backend will be available at: http://localhost:8000

## ⚛️ Frontend Setup

### 1. Open New Terminal and Navigate to Frontend
```bash
cd frontend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm start
```

The frontend will be available at: http://localhost:3000

## 🚀 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Development Commands

### Backend Commands
```bash
# Activate virtual environment (if not active)
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows

# Start development server
uvicorn app.main:app --reload

# Create new migration
python -m alembic revision --autogenerate -m "Description"

# Apply migrations
python -m alembic upgrade head

# Run tests
pytest

# Install new package
pip install package_name
pip freeze > requirements.txt
```

### Frontend Commands
```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Install new package
npm install package_name
```

## 🛠 Troubleshooting

### Common Issues

#### 1. Database Connection Error
```bash
# Check if PostgreSQL is running
# Windows: Check Services
# macOS: brew services list | grep postgresql
# Linux: sudo systemctl status postgresql

# Test connection
psql -U virsnapp_user -d virsnapp_db -h localhost
```

#### 2. Python Virtual Environment Issues
```bash
# Delete and recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### 3. Node.js Dependencies Issues
```bash
# Clear npm cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### 4. Port Already in Use
```bash
# Check what's using the port
# Windows: netstat -ano | findstr :8000
# macOS/Linux: lsof -i :8000

# Kill process using port
# Windows: taskkill /PID <PID> /F
# macOS/Linux: kill -9 <PID>
```

#### 5. YouTube API Issues
- Ensure your API key is valid
- Check quota limits in Google Cloud Console
- Verify the YouTube Data API v3 is enabled

### Environment Variables Check
```bash
# Backend - check if variables are loaded
cd backend
python -c "from app.config import settings; print(settings.youtube_api_key[:10] + '...')"

# Frontend - check if React app can access variables
echo $REACT_APP_API_URL  # Should show http://localhost:8000
```

## 📝 Development Workflow

### 1. Starting Development Session
```bash
# Terminal 1: Start PostgreSQL (if not running as service)
# Windows: Start from Services
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql

# Terminal 2: Start Backend
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn app.main:app --reload

# Terminal 3: Start Frontend
cd frontend
npm start
```

### 2. Making Changes
- Backend changes will auto-reload due to `--reload` flag
- Frontend changes will auto-reload due to React's hot reloading
- Database schema changes require new migrations

### 3. Adding New Features
```bash
# Backend: Add new endpoint
# 1. Create/modify models in app/models/
# 2. Create migration: python -m alembic revision --autogenerate -m "Add feature"
# 3. Apply migration: python -m alembic upgrade head
# 4. Add service logic in app/services/
# 5. Add API routes in app/routers/
# 6. Update schemas in app/schemas/

# Frontend: Add new component
# 1. Create component in src/components/
# 2. Add styles in corresponding .css file
# 3. Import and use in Dashboard.js or create new page
# 4. Add API calls in src/services/api.js if needed
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest -v
pytest --cov=app --cov-report=html
```

### Frontend Testing
```bash
cd frontend
npm test
npm test -- --coverage --watchAll=false
```

## 📊 Monitoring in Development

### View Logs
- Backend logs appear in the terminal where uvicorn is running
- Frontend logs appear in browser console
- Database logs depend on PostgreSQL configuration

### API Testing
- Use the interactive docs at http://localhost:8000/docs
- Use tools like Postman or curl for API testing
- Browser network tab for frontend API calls

This setup gives you full control over the development environment without Docker dependencies.
