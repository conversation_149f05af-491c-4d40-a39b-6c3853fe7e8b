.channel-card {
  position: relative;
  overflow: hidden;
}

.channel-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.channel-card__info {
  display: flex;
  gap: var(--spacing-3);
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.channel-card__thumbnail {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  object-fit: cover;
  flex-shrink: 0;
  border: 2px solid var(--color-border);
}

.channel-card__details {
  min-width: 0;
  flex: 1;
}

.channel-card__name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-1) 0;
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.channel-card__id {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  font-family: var(--font-family-mono);
  margin: 0 0 var(--spacing-2) 0;
}

.channel-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.channel-card__actions {
  display: flex;
  gap: var(--spacing-1);
  flex-shrink: 0;
}

.channel-card__action {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.channel-card__action:hover {
  background-color: var(--color-bg-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text-primary);
}

.channel-card__action--view:hover {
  color: var(--color-info);
  border-color: var(--color-info);
}

.channel-card__action--refresh:hover {
  color: var(--color-accent-primary);
  border-color: var(--color-accent-primary);
}

.channel-card__action--remove:hover {
  color: var(--color-error);
  border-color: var(--color-error);
}

.channel-card__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.channel-card__stat {
  text-align: center;
  padding: var(--spacing-3);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.channel-card__stat-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-1);
}

.channel-card__stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-primary);
  line-height: var(--line-height-tight);
}

.channel-card__footer {
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-border);
}

.channel-card__last-updated {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  font-style: italic;
}

/* Responsive design */
@media (max-width: 480px) {
  .channel-card__header {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .channel-card__actions {
    align-self: flex-end;
  }
  
  .channel-card__stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .channel-card__stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
  
  .channel-card__stat-label,
  .channel-card__stat-value {
    display: inline;
  }
  
  .channel-card__stat-value {
    font-size: var(--font-size-lg);
  }
}

/* Loading state */
.channel-card--loading {
  opacity: 0.6;
  pointer-events: none;
}

.channel-card--loading .channel-card__action {
  cursor: not-allowed;
}

/* Error state */
.channel-card--error {
  border-color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.05);
}

.channel-card--error .channel-card__name {
  color: var(--color-error);
}
