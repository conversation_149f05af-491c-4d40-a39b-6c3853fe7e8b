#!/bin/bash

# Virsnapp Setup Script
# This script automates the initial setup of the Virsnapp application

set -e  # Exit on any error

echo "🚀 Virsnapp Setup Script"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker and Docker Compose are installed${NC}"
}

# Create environment files
setup_environment() {
    echo -e "${BLUE}📝 Setting up environment files...${NC}"
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Created .env file${NC}"
    else
        echo -e "${YELLOW}⚠️  .env file already exists${NC}"
    fi
    
    if [ ! -f backend/.env ]; then
        cp backend/.env.example backend/.env
        echo -e "${GREEN}✅ Created backend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  backend/.env file already exists${NC}"
    fi
    
    if [ ! -f frontend/.env ]; then
        cp frontend/.env.example frontend/.env
        echo -e "${GREEN}✅ Created frontend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  frontend/.env file already exists${NC}"
    fi
}

# Prompt for YouTube API key
setup_youtube_api() {
    echo -e "${BLUE}🔑 YouTube API Configuration${NC}"
    echo "You need a YouTube Data API v3 key from Google Cloud Console"
    echo "Visit: https://console.cloud.google.com/"
    echo ""
    
    read -p "Enter your YouTube API key (or press Enter to skip): " youtube_api_key
    
    if [ ! -z "$youtube_api_key" ]; then
        # Update .env file
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/your_youtube_api_key_here/${youtube_api_key}/" .env
            sed -i '' "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        else
            # Linux
            sed -i "s/your_youtube_api_key_here/${youtube_api_key}/" .env
            sed -i "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        fi
        echo -e "${GREEN}✅ YouTube API key configured${NC}"
    else
        echo -e "${YELLOW}⚠️  YouTube API key skipped - you'll need to configure it manually${NC}"
    fi
}

# Generate secure keys
generate_secrets() {
    echo -e "${BLUE}🔐 Generating secure keys...${NC}"
    
    # Generate secret key
    secret_key=$(openssl rand -hex 32)
    postgres_password=$(openssl rand -hex 16)
    
    # Update .env files
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your-super-secret-key-change-in-production/${secret_key}/" .env
        sed -i '' "s/your-super-secret-key-change-in-production/${secret_key}/" backend/.env
        sed -i '' "s/virsnapp_password/${postgres_password}/" .env
        sed -i '' "s/virsnapp_password/${postgres_password}/" backend/.env
    else
        # Linux
        sed -i "s/your-super-secret-key-change-in-production/${secret_key}/" .env
        sed -i "s/your-super-secret-key-change-in-production/${secret_key}/" backend/.env
        sed -i "s/virsnapp_password/${postgres_password}/" .env
        sed -i "s/virsnapp_password/${postgres_password}/" backend/.env
    fi
    
    echo -e "${GREEN}✅ Secure keys generated${NC}"
}

# Build and start services
start_services() {
    echo -e "${BLUE}🏗️  Building and starting services...${NC}"
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    echo -e "${GREEN}✅ Services started${NC}"
}

# Wait for services to be ready
wait_for_services() {
    echo -e "${BLUE}⏳ Waiting for services to be ready...${NC}"
    
    # Wait for database
    echo "Waiting for database..."
    while ! docker-compose exec -T postgres pg_isready -U virsnapp_user > /dev/null 2>&1; do
        sleep 1
    done
    
    # Wait for backend
    echo "Waiting for backend..."
    while ! curl -f http://localhost:8000/health > /dev/null 2>&1; do
        sleep 1
    done
    
    # Wait for frontend
    echo "Waiting for frontend..."
    while ! curl -f http://localhost:3000 > /dev/null 2>&1; do
        sleep 1
    done
    
    echo -e "${GREEN}✅ All services are ready${NC}"
}

# Run database migrations
run_migrations() {
    echo -e "${BLUE}🗄️  Running database migrations...${NC}"
    
    docker-compose exec backend python -m alembic upgrade head
    
    echo -e "${GREEN}✅ Database migrations completed${NC}"
}

# Display final information
display_info() {
    echo ""
    echo -e "${GREEN}🎉 Virsnapp is now running!${NC}"
    echo "=========================="
    echo ""
    echo "📱 Frontend:     http://localhost:3000"
    echo "🔧 Backend API:  http://localhost:8000"
    echo "📚 API Docs:     http://localhost:8000/docs"
    echo "❤️  Health Check: http://localhost:8000/health"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Add your first YouTube channel"
    echo "3. Explore the dashboard and statistics"
    echo ""
    echo -e "${BLUE}🛠️  Useful Commands:${NC}"
    echo "• View logs:          docker-compose logs -f"
    echo "• Stop services:      docker-compose down"
    echo "• Restart services:   docker-compose restart"
    echo "• Update services:    docker-compose pull && docker-compose up -d"
    echo ""
    echo -e "${YELLOW}⚠️  Don't forget to configure your YouTube API key if you skipped it!${NC}"
}

# Main execution
main() {
    echo "Starting Virsnapp setup..."
    echo ""
    
    check_docker
    setup_environment
    setup_youtube_api
    generate_secrets
    start_services
    wait_for_services
    run_migrations
    display_info
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
