  - Desktop: `> 1024px`
- **Sidebar Navigation**: Collapsible sidebar with overlay on mobile

### Accessibility
- **WCAG 2.1 AA Compliance**: Proper color contrast and focus management
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Semantic HTML and ARIA labels
- **Skip Links**: Navigation bypass for screen readers

## 🧪 Testing

### Backend Testing
```bash
cd backend

# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html
```

### Frontend Testing
```bash
cd frontend

# Run tests
npm test

# Run tests with coverage
npm test -- --coverage --watchAll=false

# Run e2e tests (if configured)
npm run test:e2e
```

## 📝 Monitoring and Logging

### Application Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# View logs with timestamps
docker-compose logs -f -t
```

### Health Monitoring
- **Backend Health**: `GET /health`
- **Frontend Health**: `GET /health` (via nginx)
- **Database Health**: Automatic health checks in Docker Compose
- **Redis Health**: Automatic health checks in Docker Compose

## 🔧 Troubleshooting

### Common Issues

#### 1. YouTube API Quota Exceeded
```bash
# Check API usage in Google Cloud Console
# Implement request caching
# Add rate limiting to reduce API calls
```

#### 2. Database Connection Issues
```bash
# Check database container status
docker-compose logs postgres

# Verify environment variables
docker-compose exec backend env | grep DATABASE_URL

# Test database connection
docker-compose exec backend python -c "from app.database import engine; print(engine.connect())"
```

#### 3. Frontend Build Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for version conflicts
npm audit
```

#### 4. SSL Certificate Issues (Production)
```bash
# Verify certificate files
ls -la ssl/

# Test certificate validity
openssl x509 -in ssl/cert.pem -text -noout

# Check nginx configuration
docker-compose exec nginx nginx -t
```

### Performance Optimization

#### Backend
- **Database Indexing**: Proper indexes on frequently queried columns
- **Connection Pooling**: Configured SQLAlchemy pool settings
- **Async Processing**: FastAPI async endpoints for better concurrency
- **Caching**: Redis for frequently accessed data

#### Frontend
- **Code Splitting**: React lazy loading for routes
- **Image Optimization**: Proper image formats and sizes
- **Bundle Analysis**: Use webpack-bundle-analyzer
- **Service Workers**: For offline capability (optional)

#### Database
```sql
-- Useful performance queries
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'channels' OR tablename = 'statistics';
```

## 🔄 Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec postgres pg_dump -U virsnapp_user virsnapp_db > backup.sql

# Restore from backup
docker-compose exec -T postgres psql -U virsnapp_user virsnapp_db < backup.sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec postgres pg_dump -U virsnapp_user virsnapp_db > "backup_${DATE}.sql"
find . -name "backup_*.sql" -mtime +7 -delete
```

### Volume Backup
```bash
# Backup PostgreSQL data volume
docker run --rm -v virsnapp_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data

# Backup Redis data volume
docker run --rm -v virsnapp_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz /data
```

## 🚀 Scaling and Production Considerations

### Horizontal Scaling
```yaml
# Scale backend instances
services:
  backend:
    deploy:
      replicas: 3
    
  # Add load balancer
  nginx:
    depends_on:
      - backend
```

### Environment-specific Configurations

#### Development
- Debug mode enabled
- Auto-reload on changes
- Detailed error messages
- Development databases

#### Production
- Debug mode disabled
- Error logging and monitoring
- SSL certificates
- Production databases
- Rate limiting
- Security headers

### Security Checklist

#### Backend Security
- [ ] Secure secret key generation
- [ ] Input validation and sanitization
- [ ] SQL injection prevention (SQLAlchemy ORM)
- [ ] CORS configuration
- [ ] Rate limiting
- [ ] Error handling without information leakage

#### Frontend Security
- [ ] Content Security Policy (CSP)
- [ ] XSS protection
- [ ] Secure cookie settings
- [ ] HTTPS enforcement
- [ ] Input sanitization

#### Infrastructure Security
- [ ] Regular dependency updates
- [ ] Container vulnerability scanning
- [ ] Network segmentation
- [ ] Access logging
- [ ] Backup encryption

## 📈 Performance Benchmarks

### Expected Performance
- **API Response Time**: < 200ms for most endpoints
- **Database Queries**: < 100ms for indexed queries
- **Frontend Load Time**: < 3s on 3G connection
- **Concurrent Users**: 100+ with proper scaling

### Load Testing
```bash
# Install tools
pip install locust

# Create load test script
# Run load tests
locust -f tests/load_test.py --host=http://localhost:8000
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Standards
- **Python**: Follow PEP 8, use Black formatter
- **JavaScript**: Use Prettier, ESLint configuration
- **Git**: Conventional commit messages
- **Documentation**: Update README for new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation at `/docs`

## 🔗 Useful Links

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [YouTube Data API](https://developers.google.com/youtube/v3)
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

**Built with ❤️ using modern web technologies**
