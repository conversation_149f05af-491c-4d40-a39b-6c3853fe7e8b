#!/bin/bash

# Virsnapp Local Development Setup Script
# This script sets up the application for local development without Docker

set -e  # Exit on any error

echo "🚀 Virsnapp Local Development Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required tools are installed
check_requirements() {
    echo -e "${BLUE}🔍 Checking requirements...${NC}"
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python 3.11+ is required but not installed.${NC}"
        echo "Please install Python from https://python.org"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 18+ is required but not installed.${NC}"
        echo "Please install Node.js from https://nodejs.org"
        exit 1
    fi
    
    # Check PostgreSQL
    if ! command -v psql &> /dev/null; then
        echo -e "${RED}❌ PostgreSQL is required but not installed.${NC}"
        echo "Please install PostgreSQL:"
        echo "  macOS: brew install postgresql"
        echo "  Linux: sudo apt-get install postgresql postgresql-contrib"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All requirements are installed${NC}"
}

# Setup environment files
setup_environment() {
    echo -e "${BLUE}📝 Setting up environment files...${NC}"
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Created .env file${NC}"
    else
        echo -e "${YELLOW}⚠️  .env file already exists${NC}"
    fi
    
    if [ ! -f backend/.env ]; then
        cp backend/.env.example backend/.env
        echo -e "${GREEN}✅ Created backend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  backend/.env file already exists${NC}"
    fi
    
    if [ ! -f frontend/.env ]; then
        cp frontend/.env.example frontend/.env
        echo -e "${GREEN}✅ Created frontend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  frontend/.env file already exists${NC}"
    fi
}

# Configure database connection for local development
configure_database() {
    echo -e "${BLUE}🗄️  Configuring database connection...${NC}"
    
    # Update backend/.env for local PostgreSQL
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' 's|DATABASE_URL=.*|DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db|' backend/.env
    else
        # Linux
        sed -i 's|DATABASE_URL=.*|DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db|' backend/.env
    fi
    
    echo -e "${GREEN}✅ Database connection configured${NC}"
}

# Setup database
setup_database() {
    echo -e "${BLUE}🗄️  Setting up PostgreSQL database...${NC}"
    
    # Check if PostgreSQL is running
    if ! pg_isready -q; then
        echo -e "${YELLOW}⚠️  PostgreSQL is not running. Attempting to start...${NC}"
        
        # Try to start PostgreSQL based on OS
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS with Homebrew
            if command -v brew &> /dev/null; then
                brew services start postgresql
            else
                echo -e "${RED}❌ Please start PostgreSQL manually${NC}"
                exit 1
            fi
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            sudo systemctl start postgresql
        fi
        
        # Wait a moment for PostgreSQL to start
        sleep 3
    fi
    
    # Create database and user
    echo "Creating database and user..."
    
    # Create the database setup SQL
    cat > /tmp/setup_virsnapp_db.sql << EOF
-- Create user if not exists
DO \$\$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'virsnapp_user') THEN
      CREATE USER virsnapp_user WITH PASSWORD 'virsnapp_password';
   END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE virsnapp_db OWNER virsnapp_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'virsnapp_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE virsnapp_db TO virsnapp_user;
EOF
    
    # Execute the SQL script
    if psql -U postgres -f /tmp/setup_virsnapp_db.sql; then
        echo -e "${GREEN}✅ Database and user created successfully${NC}"
    else
        echo -e "${RED}❌ Failed to create database. You may need to run:${NC}"
        echo "sudo -u postgres psql -f /tmp/setup_virsnapp_db.sql"
        echo "Or create the database manually following the LOCAL_SETUP.md instructions"
    fi
    
    # Clean up temp file
    rm -f /tmp/setup_virsnapp_db.sql
}

# Setup backend
setup_backend() {
    echo -e "${BLUE}🐍 Setting up Python backend...${NC}"
    
    cd backend
    
    # Create virtual environment
    echo "Creating virtual environment..."
    python3 -m venv venv
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    echo "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Run database migrations
    echo "Running database migrations..."
    python -m alembic upgrade head
    
    echo -e "${GREEN}✅ Backend setup completed${NC}"
    
    cd ..
}

# Setup frontend
setup_frontend() {
    echo -e "${BLUE}⚛️  Setting up React frontend...${NC}"
    
    cd frontend
    
    # Install dependencies
    echo "Installing Node.js dependencies..."
    npm install
    
    echo -e "${GREEN}✅ Frontend setup completed${NC}"
    
    cd ..
}

# Prompt for YouTube API key
setup_youtube_api() {
    echo -e "${BLUE}🔑 YouTube API Configuration${NC}"
    echo "You need a YouTube Data API v3 key from Google Cloud Console"
    echo "Visit: https://console.cloud.google.com/"
    echo ""
    
    read -p "Enter your YouTube API key (or press Enter to skip): " youtube_api_key
    
    if [ ! -z "$youtube_api_key" ]; then
        # Update backend/.env file
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        else
            # Linux
            sed -i "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        fi
        echo -e "${GREEN}✅ YouTube API key configured${NC}"
    else
        echo -e "${YELLOW}⚠️  YouTube API key skipped - you can configure it later in backend/.env${NC}"
    fi
}

# Generate secure secret key
generate_secret_key() {
    echo -e "${BLUE}🔐 Generating secure secret key...${NC}"
    
    # Generate secret key
    secret_key=$(openssl rand -hex 32)
    
    # Update backend/.env file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your-super-secret-key-change-in-development/${secret_key}/" backend/.env
    else
        # Linux
        sed -i "s/your-super-secret-key-change-in-development/${secret_key}/" backend/.env
    fi
    
    echo -e "${GREEN}✅ Secret key generated${NC}"
}

# Create start scripts
create_start_scripts() {
    echo -e "${BLUE}📜 Creating start scripts...${NC}"
    
    # Create backend start script
    cat > start_backend.sh << 'EOF'
#!/bin/bash
echo "🐍 Starting Virsnapp Backend..."
cd backend
source venv/bin/activate
echo "Backend starting at http://localhost:8000"
echo "API docs available at http://localhost:8000/docs"
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
EOF
    
    # Create frontend start script
    cat > start_frontend.sh << 'EOF'
#!/bin/bash
echo "⚛️  Starting Virsnapp Frontend..."
cd frontend
echo "Frontend starting at http://localhost:3000"
npm start
EOF
    
    # Make scripts executable
    chmod +x start_backend.sh start_frontend.sh
    
    echo -e "${GREEN}✅ Start scripts created${NC}"
}

# Display final information
display_info() {
    echo ""
    echo -e "${GREEN}🎉 Virsnapp local development setup completed!${NC}"
    echo "============================================="
    echo ""
    echo -e "${BLUE}🚀 Starting the application:${NC}"
    echo ""
    echo "1. Start the backend (in one terminal):"
    echo "   ./start_backend.sh"
    echo ""
    echo "2. Start the frontend (in another terminal):"
    echo "   ./start_frontend.sh"
    echo ""
    echo -e "${BLUE}🌐 Access URLs:${NC}"
    echo "• Frontend:     http://localhost:3000"
    echo "• Backend API:  http://localhost:8000"
    echo "• API Docs:     http://localhost:8000/docs"
    echo "• Health Check: http://localhost:8000/health"
    echo ""
    echo -e "${BLUE}🛠️  Development Commands:${NC}"
    echo "• Backend tests:    cd backend && source venv/bin/activate && pytest"
    echo "• Frontend tests:   cd frontend && npm test"
    echo "• New migration:    cd backend && source venv/bin/activate && python -m alembic revision --autogenerate -m 'Description'"
    echo "• Apply migration:  cd backend && source venv/bin/activate && python -m alembic upgrade head"
    echo ""
    if [ -z "$youtube_api_key" ]; then
        echo -e "${YELLOW}⚠️  Remember to add your YouTube API key to backend/.env${NC}"
    fi
    echo -e "${GREEN}Happy coding! 🚀${NC}"
}

# Main execution
main() {
    echo "Starting Virsnapp local development setup..."
    echo ""
    
    check_requirements
    setup_environment
    configure_database
    setup_database
    setup_youtube_api
    generate_secret_key
    setup_backend
    setup_frontend
    create_start_scripts
    display_info
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
