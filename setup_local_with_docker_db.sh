#!/bin/bash

# Virsnapp Local Development Setup with Docker Database
# This script sets up local development with PostgreSQL in Docker

set -e  # Exit on any error

echo "🚀 Virsnapp Local Development Setup (Docker DB)"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required tools are installed
check_requirements() {
    echo -e "${BLUE}🔍 Checking requirements...${NC}"
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python 3.11+ is required but not installed.${NC}"
        echo "Please install Python from https://python.org"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 18+ is required but not installed.${NC}"
        echo "Please install Node.js from https://nodejs.org"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is required but not installed.${NC}"
        echo "Please install Docker from https://docker.com"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is required but not installed.${NC}"
        echo "Please install Docker Compose"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All requirements are installed${NC}"
}

# Setup environment files
setup_environment() {
    echo -e "${BLUE}📝 Setting up environment files...${NC}"
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Created .env file${NC}"
    else
        echo -e "${YELLOW}⚠️  .env file already exists${NC}"
    fi
    
    if [ ! -f backend/.env ]; then
        cp backend/.env.example backend/.env
        echo -e "${GREEN}✅ Created backend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  backend/.env file already exists${NC}"
    fi
    
    if [ ! -f frontend/.env ]; then
        cp frontend/.env.example frontend/.env
        echo -e "${GREEN}✅ Created frontend/.env file${NC}"
    else
        echo -e "${YELLOW}⚠️  frontend/.env file already exists${NC}"
    fi
}

# Configure database connection for Docker PostgreSQL
configure_database() {
    echo -e "${BLUE}🗄️  Configuring database connection for Docker...${NC}"
    
    # Update backend/.env for Docker PostgreSQL
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' 's|DATABASE_URL=.*|DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db|' backend/.env
        sed -i '' 's|REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' backend/.env
    else
        # Linux
        sed -i 's|DATABASE_URL=.*|DATABASE_URL=postgresql://virsnapp_user:virsnapp_password@localhost:5432/virsnapp_db|' backend/.env
        sed -i 's|REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' backend/.env
    fi
    
    echo -e "${GREEN}✅ Database connection configured for Docker${NC}"
}

# Start Docker database services
start_database() {
    echo -e "${BLUE}🐳 Starting PostgreSQL in Docker...${NC}"
    
    # Start database services
    docker-compose -f docker-compose.db.yml up -d
    
    echo -e "${BLUE}⏳ Waiting for database to be ready...${NC}"
    
    # Wait for PostgreSQL to be ready
    for i in {1..30}; do
        if docker-compose -f docker-compose.db.yml exec -T postgres pg_isready -U virsnapp_user -d virsnapp_db > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Database is ready${NC}"
            break
        fi
        
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Database failed to start after 30 attempts${NC}"
            echo "Check logs with: docker-compose -f docker-compose.db.yml logs postgres"
            exit 1
        fi
        
        echo "Waiting for database... ($i/30)"
        sleep 2
    done
}

# Setup backend
setup_backend() {
    echo -e "${BLUE}🐍 Setting up Python backend...${NC}"
    
    cd backend
    
    # Create virtual environment
    echo "Creating virtual environment..."
    python3 -m venv venv
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    echo "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Run database migrations
    echo "Running database migrations..."
    python -m alembic upgrade head
    
    echo -e "${GREEN}✅ Backend setup completed${NC}"
    
    cd ..
}

# Setup frontend
setup_frontend() {
    echo -e "${BLUE}⚛️  Setting up React frontend...${NC}"
    
    cd frontend
    
    # Install dependencies
    echo "Installing Node.js dependencies..."
    npm install
    
    echo -e "${GREEN}✅ Frontend setup completed${NC}"
    
    cd ..
}

# Prompt for YouTube API key
setup_youtube_api() {
    echo -e "${BLUE}🔑 YouTube API Configuration${NC}"
    echo "You need a YouTube Data API v3 key from Google Cloud Console"
    echo "Visit: https://console.cloud.google.com/"
    echo ""
    
    read -p "Enter your YouTube API key (or press Enter to skip): " youtube_api_key
    
    if [ ! -z "$youtube_api_key" ]; then
        # Update backend/.env file
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        else
            # Linux
            sed -i "s/your_youtube_api_key_here/${youtube_api_key}/" backend/.env
        fi
        echo -e "${GREEN}✅ YouTube API key configured${NC}"
    else
        echo -e "${YELLOW}⚠️  YouTube API key skipped - you can configure it later in backend/.env${NC}"
    fi
}

# Generate secure secret key
generate_secret_key() {
    echo -e "${BLUE}🔐 Generating secure secret key...${NC}"
    
    # Generate secret key
    secret_key=$(openssl rand -hex 32)
    
    # Update backend/.env file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your-super-secret-key-change-in-production/${secret_key}/" backend/.env
    else
        # Linux
        sed -i "s/your-super-secret-key-change-in-production/${secret_key}/" backend/.env
    fi
    
    echo -e "${GREEN}✅ Secret key generated${NC}"
}

# Create start scripts
create_start_scripts() {
    echo -e "${BLUE}📜 Creating start scripts...${NC}"
    
    # Create database start script
    cat > start_database.sh << 'EOF'
#!/bin/bash
echo "🐳 Starting Virsnapp Database Services..."
docker-compose -f docker-compose.db.yml up -d
echo "✅ Database services started"
echo ""
echo "🌐 Available services:"
echo "  • PostgreSQL: localhost:5432 (virsnapp_user/virsnapp_password)"
echo "  • Redis: localhost:6379"
echo "  • pgAdmin: http://localhost:5050 (<EMAIL>/admin123)"
echo ""
echo "🛠️  Useful commands:"
echo "  • View logs: docker-compose -f docker-compose.db.yml logs -f"
echo "  • Stop services: docker-compose -f docker-compose.db.yml down"
echo "  • Connect to DB: docker exec -it virsnapp_postgres_local psql -U virsnapp_user -d virsnapp_db"
EOF

    # Create database stop script
    cat > stop_database.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping Virsnapp Database Services..."
docker-compose -f docker-compose.db.yml down
echo "✅ Database services stopped"
EOF

    # Create backend start script
    cat > start_backend.sh << 'EOF'
#!/bin/bash
echo "🐍 Starting Virsnapp Backend..."

# Check if database is running
if ! docker ps | grep -q virsnapp_postgres_local; then
    echo "⚠️  Database not running. Starting database first..."
    ./start_database.sh
    echo "⏳ Waiting for database to be ready..."
    sleep 5
fi

cd backend
source venv/bin/activate
echo "Backend starting at http://localhost:8000"
echo "API docs available at http://localhost:8000/docs"
echo "Press Ctrl+C to stop the server"
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
EOF

    # Create frontend start script
    cat > start_frontend.sh << 'EOF'
#!/bin/bash
echo "⚛️  Starting Virsnapp Frontend..."
cd frontend
echo "Frontend starting at http://localhost:3000"
echo "Press Ctrl+C to stop the server"
npm start
EOF

    # Create complete start script
    cat > start_all.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Complete Virsnapp Application..."
echo "==========================================="

# Start database
echo "1. Starting database services..."
./start_database.sh

# Wait for database
echo "2. Waiting for database to be ready..."
sleep 5

echo "3. Starting backend and frontend..."
echo ""
echo "🌐 Application URLs:"
echo "  • Frontend: http://localhost:3000"
echo "  • Backend API: http://localhost:8000"
echo "  • API Docs: http://localhost:8000/docs"
echo "  • pgAdmin: http://localhost:5050"
echo ""

# Start backend and frontend in background
gnome-terminal -- bash -c "./start_backend.sh" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "./start_backend.sh"' 2>/dev/null || \
echo "Please run './start_backend.sh' in a new terminal"

sleep 2

gnome-terminal -- bash -c "./start_frontend.sh" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "./start_frontend.sh"' 2>/dev/null || \
echo "Please run './start_frontend.sh' in a new terminal"

echo "✅ All services are starting..."
echo "Note: If terminals didn't open automatically, run the scripts manually in separate terminals."
EOF

    # Make scripts executable
    chmod +x start_database.sh stop_database.sh start_backend.sh start_frontend.sh start_all.sh
    
    echo -e "${GREEN}✅ Start scripts created${NC}"
}

# Test the setup
test_setup() {
    echo -e "${BLUE}🧪 Testing setup...${NC}"
    
    # Test database connection
    cd backend
    source venv/bin/activate
    
    if python -c "from app.database import engine; engine.connect().close(); print('Database connection successful!')" 2>/dev/null; then
        echo -e "${GREEN}✅ Database connection test passed${NC}"
    else
        echo -e "${YELLOW}⚠️  Database connection test failed - but this might be normal if you haven't started the app yet${NC}"
    fi
    
    cd ..
}

# Display final information
display_info() {
    echo ""
    echo -e "${GREEN}🎉 Virsnapp local development setup completed!${NC}"
    echo "============================================="
    echo ""
    echo -e "${BLUE}🚀 Quick Start Options:${NC}"
    echo ""
    echo "1. Start everything at once:"
    echo "   ./start_all.sh"
    echo ""
    echo "2. Start services individually:"
    echo "   ./start_database.sh    # Start PostgreSQL & Redis"
    echo "   ./start_backend.sh     # Start Python API"
    echo "   ./start_frontend.sh    # Start React app"
    echo ""
    echo -e "${BLUE}🌐 Access URLs:${NC}"
    echo "• Frontend:     http://localhost:3000"
    echo "• Backend API:  http://localhost:8000"
    echo "• API Docs:     http://localhost:8000/docs"
    echo "• pgAdmin:      http://localhost:5050 (<EMAIL>/admin123)"
    echo "• Database:     localhost:5432 (virsnapp_user/virsnapp_password)"
    echo ""
    echo -e "${BLUE}🛠️  Development Commands:${NC}"
    echo "• Stop database:        ./stop_database.sh"
    echo "• View database logs:   docker-compose -f docker-compose.db.yml logs -f"
    echo "• Connect to database:  docker exec -it virsnapp_postgres_local psql -U virsnapp_user -d virsnapp_db"
    echo "• Backend tests:        cd backend && source venv/bin/activate && pytest"
    echo "• Frontend tests:       cd frontend && npm test"
    echo ""
    if [ -z "$youtube_api_key" ]; then
        echo -e "${YELLOW}⚠️  Remember to add your YouTube API key to backend/.env${NC}"
    fi
    echo -e "${GREEN}Happy coding! 🚀${NC}"
}

# Main execution
main() {
    echo "Starting Virsnapp local development setup with Docker database..."
    echo ""
    
    check_requirements
    setup_environment
    configure_database
    start_database
    setup_youtube_api
    generate_secret_key
    setup_backend
    setup_frontend
    create_start_scripts
    test_setup
    display_info
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
